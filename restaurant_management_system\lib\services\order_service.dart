import 'package:sqflite/sqflite.dart';
import '../models/order.dart';
import '../models/menu_item.dart';
import 'database_service.dart';
import 'menu_service.dart';

class OrderService {
  static final OrderService _instance = OrderService._internal();
  factory OrderService() => _instance;
  OrderService._internal();

  final DatabaseService _databaseService = DatabaseService();
  final MenuService _menuService = MenuService();

  // إنشاء طلب جديد
  Future<int> createOrder(Order order) async {
    final db = await _databaseService.database;
    
    // بدء معاملة قاعدة البيانات
    return await db.transaction((txn) async {
      // إدراج الطلب
      final orderId = await txn.insert('orders', order.toMap());
      
      // إدراج عناصر الطلب
      for (final item in order.items) {
        await txn.insert('order_items', {
          'order_id': orderId,
          'menu_item_id': item.menuItemId,
          'quantity': item.quantity,
          'unit_price': item.unitPrice,
          'notes': item.notes,
        });
      }
      
      return orderId;
    });
  }

  // تحديث طلب موجود
  Future<int> updateOrder(Order order) async {
    final db = await _databaseService.database;
    
    return await db.transaction((txn) async {
      // تحديث الطلب
      final result = await txn.update(
        'orders',
        order.copyWith(updatedAt: DateTime.now()).toMap(),
        where: 'id = ?',
        whereArgs: [order.id],
      );
      
      // حذف العناصر القديمة
      await txn.delete(
        'order_items',
        where: 'order_id = ?',
        whereArgs: [order.id],
      );
      
      // إدراج العناصر الجديدة
      for (final item in order.items) {
        await txn.insert('order_items', {
          'order_id': order.id,
          'menu_item_id': item.menuItemId,
          'quantity': item.quantity,
          'unit_price': item.unitPrice,
          'notes': item.notes,
        });
      }
      
      return result;
    });
  }

  // حذف طلب
  Future<int> deleteOrder(int id) async {
    final db = await _databaseService.database;
    return await db.delete(
      'orders',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // الحصول على طلب بالمعرف
  Future<Order?> getOrderById(int id) async {
    final db = await _databaseService.database;
    
    // الحصول على بيانات الطلب
    final List<Map<String, dynamic>> orderMaps = await db.query(
      'orders',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (orderMaps.isEmpty) return null;

    final orderData = orderMaps.first;
    
    // الحصول على عناصر الطلب
    final List<Map<String, dynamic>> itemMaps = await db.query(
      'order_items',
      where: 'order_id = ?',
      whereArgs: [id],
    );

    final items = <OrderItem>[];
    for (final itemMap in itemMaps) {
      final menuItem = await _menuService.getMenuItemById(itemMap['menu_item_id']);
      items.add(OrderItem.fromMap(itemMap).copyWith(menuItem: menuItem));
    }

    return Order.fromMap(orderData).copyWith(items: items);
  }

  // الحصول على جميع الطلبات
  Future<List<Order>> getAllOrders() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'orders',
      orderBy: 'created_at DESC',
    );

    final orders = <Order>[];
    for (final map in maps) {
      final order = await getOrderById(map['id']);
      if (order != null) orders.add(order);
    }

    return orders;
  }

  // الحصول على الطلبات حسب الحالة
  Future<List<Order>> getOrdersByStatus(OrderStatus status) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'orders',
      where: 'status = ?',
      whereArgs: [status.index],
      orderBy: 'created_at DESC',
    );

    final orders = <Order>[];
    for (final map in maps) {
      final order = await getOrderById(map['id']);
      if (order != null) orders.add(order);
    }

    return orders;
  }

  // الحصول على الطلبات حسب النوع
  Future<List<Order>> getOrdersByType(OrderType type) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'orders',
      where: 'type = ?',
      whereArgs: [type.index],
      orderBy: 'created_at DESC',
    );

    final orders = <Order>[];
    for (final map in maps) {
      final order = await getOrderById(map['id']);
      if (order != null) orders.add(order);
    }

    return orders;
  }

  // الحصول على طلبات طاولة معينة
  Future<List<Order>> getOrdersByTable(int tableNumber) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'orders',
      where: 'table_number = ?',
      whereArgs: [tableNumber],
      orderBy: 'created_at DESC',
    );

    final orders = <Order>[];
    for (final map in maps) {
      final order = await getOrderById(map['id']);
      if (order != null) orders.add(order);
    }

    return orders;
  }

  // الحصول على طلبات اليوم
  Future<List<Order>> getTodayOrders() async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'orders',
      where: 'created_at >= ? AND created_at < ?',
      whereArgs: [startOfDay.toIso8601String(), endOfDay.toIso8601String()],
      orderBy: 'created_at DESC',
    );

    final orders = <Order>[];
    for (final map in maps) {
      final order = await getOrderById(map['id']);
      if (order != null) orders.add(order);
    }

    return orders;
  }

  // تحديث حالة الطلب
  Future<int> updateOrderStatus(int orderId, OrderStatus status) async {
    final db = await _databaseService.database;
    return await db.update(
      'orders',
      {
        'status': status.index,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [orderId],
    );
  }

  // تحديث حالة الدفع
  Future<int> updatePaymentStatus(int orderId, bool isPaid, PaymentMethod? paymentMethod) async {
    final db = await _databaseService.database;
    return await db.update(
      'orders',
      {
        'is_paid': isPaid ? 1 : 0,
        'payment_method': paymentMethod?.index,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [orderId],
    );
  }

  // إنشاء رقم طلب جديد
  Future<String> generateOrderNumber() async {
    final now = DateTime.now();
    final datePrefix = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
    
    final db = await _databaseService.database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM orders WHERE order_number LIKE ?',
      ['$datePrefix%'],
    );
    
    final count = Sqflite.firstIntValue(result) ?? 0;
    return '$datePrefix${(count + 1).toString().padLeft(3, '0')}';
  }

  // البحث في الطلبات
  Future<List<Order>> searchOrders(String query) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'orders',
      where: 'order_number LIKE ? OR customer_name LIKE ? OR customer_phone LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'created_at DESC',
    );

    final orders = <Order>[];
    for (final map in maps) {
      final order = await getOrderById(map['id']);
      if (order != null) orders.add(order);
    }

    return orders;
  }

  // إحصائيات الطلبات
  Future<Map<String, int>> getOrdersStatistics() async {
    final db = await _databaseService.database;
    
    final totalResult = await db.rawQuery('SELECT COUNT(*) as count FROM orders');
    final pendingResult = await db.rawQuery('SELECT COUNT(*) as count FROM orders WHERE status = 0');
    final completedResult = await db.rawQuery('SELECT COUNT(*) as count FROM orders WHERE status = 3');
    final todayResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM orders WHERE DATE(created_at) = DATE(?)',
      [DateTime.now().toIso8601String()],
    );

    return {
      'total': Sqflite.firstIntValue(totalResult) ?? 0,
      'pending': Sqflite.firstIntValue(pendingResult) ?? 0,
      'completed': Sqflite.firstIntValue(completedResult) ?? 0,
      'today': Sqflite.firstIntValue(todayResult) ?? 0,
    };
  }
}
