import 'package:flutter/services.dart';

class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  bool _soundEnabled = true;
  
  // تشغيل/إيقاف الأصوات
  void setSoundEnabled(bool enabled) {
    _soundEnabled = enabled;
  }

  bool get isSoundEnabled => _soundEnabled;

  // تشغيل صوت طلب جديد
  Future<void> playNewOrderSound() async {
    if (!_soundEnabled) return;
    
    try {
      await SystemSound.play(SystemSoundType.alert);
      // يمكن إضافة صوت مخصص هنا لاحقاً
    } catch (e) {
      print('خطأ في تشغيل الصوت: $e');
    }
  }

  // تشغيل صوت عند جاهزية الطلب
  Future<void> playOrderReadySound() async {
    if (!_soundEnabled) return;
    
    try {
      await SystemSound.play(SystemSoundType.click);
      // يمكن إضافة صوت مخصص هنا لاحقاً
    } catch (e) {
      print('خطأ في تشغيل الصوت: $e');
    }
  }

  // تشغيل صوت تحذير للطلبات المتأخرة
  Future<void> playDelayWarningSound() async {
    if (!_soundEnabled) return;
    
    try {
      // تشغيل صوت تحذير متكرر
      for (int i = 0; i < 3; i++) {
        await SystemSound.play(SystemSoundType.alert);
        await Future.delayed(const Duration(milliseconds: 200));
      }
    } catch (e) {
      print('خطأ في تشغيل الصوت: $e');
    }
  }

  // تشغيل صوت إشعار عام
  Future<void> playNotificationSound() async {
    if (!_soundEnabled) return;
    
    try {
      await SystemSound.play(SystemSoundType.click);
    } catch (e) {
      print('خطأ في تشغيل الصوت: $e');
    }
  }
}
