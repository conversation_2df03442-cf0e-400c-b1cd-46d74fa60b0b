import 'menu_item.dart';

enum OrderType { dineIn, takeaway }

enum OrderStatus { pending, preparing, ready, completed, cancelled }

enum PaymentMethod { cash, card, deferred }

class OrderItem {
  final int? id;
  final String menuItemId;
  final MenuItem? menuItem;
  final int quantity;
  final double unitPrice;
  final String? notes;

  OrderItem({
    this.id,
    required this.menuItemId,
    this.menuItem,
    required this.quantity,
    required this.unitPrice,
    this.notes,
  });

  double get totalPrice => quantity * unitPrice;

  factory OrderItem.fromMap(Map<String, dynamic> map) {
    return OrderItem(
      id: map['id'],
      menuItemId: map['menu_item_id'],
      quantity: map['quantity'] ?? 1,
      unitPrice: (map['unit_price'] ?? 0.0).toDouble(),
      notes: map['notes'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'menu_item_id': menuItemId,
      'quantity': quantity,
      'unit_price': unitPrice,
      'notes': notes,
    };
  }

  OrderItem copyWith({
    int? id,
    String? menuItemId,
    MenuItem? menuItem,
    int? quantity,
    double? unitPrice,
    String? notes,
  }) {
    return OrderItem(
      id: id ?? this.id,
      menuItemId: menuItemId ?? this.menuItemId,
      menuItem: menuItem ?? this.menuItem,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      notes: notes ?? this.notes,
    );
  }
}

class Order {
  final String id;
  final String orderNumber;
  final OrderType type;
  final OrderStatus status;
  final int? tableNumber;
  final String? customerName;
  final String? customerPhone;
  final List<OrderItem> items;
  final double subtotal;
  final double taxAmount;
  final double discountAmount;
  final double totalAmount;
  final PaymentMethod? paymentMethod;
  final bool isPaid;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  Order({
    required this.id,
    required this.orderNumber,
    required this.type,
    this.status = OrderStatus.pending,
    this.tableNumber,
    this.customerName,
    this.customerPhone,
    required this.items,
    required this.subtotal,
    this.taxAmount = 0.0,
    this.discountAmount = 0.0,
    required this.totalAmount,
    this.paymentMethod,
    this.isPaid = false,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  factory Order.fromMap(Map<String, dynamic> map) {
    return Order(
      id: map['id'],
      orderNumber: map['order_number'] ?? '',
      type: OrderType.values[map['type'] ?? 0],
      status: OrderStatus.values[map['status'] ?? 0],
      tableNumber: map['table_number'],
      customerName: map['customer_name'],
      customerPhone: map['customer_phone'],
      items: [], // سيتم تحميلها منفصلة
      subtotal: (map['subtotal'] ?? 0.0).toDouble(),
      taxAmount: (map['tax_amount'] ?? 0.0).toDouble(),
      discountAmount: (map['discount_amount'] ?? 0.0).toDouble(),
      totalAmount: (map['total_amount'] ?? 0.0).toDouble(),
      paymentMethod: map['payment_method'] != null
          ? PaymentMethod.values[map['payment_method']]
          : null,
      isPaid: (map['is_paid'] ?? 0) == 1,
      notes: map['notes'],
      createdAt:
          DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt:
          DateTime.parse(map['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'order_number': orderNumber,
      'type': type.index,
      'status': status.index,
      'table_number': tableNumber,
      'customer_name': customerName,
      'customer_phone': customerPhone,
      'subtotal': subtotal,
      'tax_amount': taxAmount,
      'discount_amount': discountAmount,
      'total_amount': totalAmount,
      'payment_method': paymentMethod?.index,
      'is_paid': isPaid ? 1 : 0,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Order copyWith({
    String? id,
    String? orderNumber,
    OrderType? type,
    OrderStatus? status,
    int? tableNumber,
    String? customerName,
    String? customerPhone,
    List<OrderItem>? items,
    double? subtotal,
    double? taxAmount,
    double? discountAmount,
    double? totalAmount,
    PaymentMethod? paymentMethod,
    bool? isPaid,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Order(
      id: id ?? this.id,
      orderNumber: orderNumber ?? this.orderNumber,
      type: type ?? this.type,
      status: status ?? this.status,
      tableNumber: tableNumber ?? this.tableNumber,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      taxAmount: taxAmount ?? this.taxAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      isPaid: isPaid ?? this.isPaid,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // حساب المجموع الفرعي من العناصر
  double calculateSubtotal() {
    return items.fold(0.0, (sum, item) => sum + item.totalPrice);
  }

  // حساب المجموع الكلي
  double calculateTotal({double taxRate = 0.0}) {
    final subtotal = calculateSubtotal();
    final tax = subtotal * taxRate;
    return subtotal + tax - discountAmount;
  }

  @override
  String toString() {
    return 'Order{id: $id, orderNumber: $orderNumber, type: $type, status: $status, totalAmount: $totalAmount}';
  }
}

// مساعدات للحصول على النصوص المترجمة
extension OrderTypeExtension on OrderType {
  String get displayName {
    switch (this) {
      case OrderType.dineIn:
        return 'داخل المطعم';
      case OrderType.takeaway:
        return 'سفري';
    }
  }

  String get displayNameEn {
    switch (this) {
      case OrderType.dineIn:
        return 'Dine In';
      case OrderType.takeaway:
        return 'Takeaway';
    }
  }
}

extension OrderStatusExtension on OrderStatus {
  String get displayName {
    switch (this) {
      case OrderStatus.pending:
        return 'في الانتظار';
      case OrderStatus.preparing:
        return 'قيد التحضير';
      case OrderStatus.ready:
        return 'جاهز';
      case OrderStatus.completed:
        return 'مكتمل';
      case OrderStatus.cancelled:
        return 'ملغي';
    }
  }

  String get displayNameEn {
    switch (this) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.preparing:
        return 'Preparing';
      case OrderStatus.ready:
        return 'Ready';
      case OrderStatus.completed:
        return 'Completed';
      case OrderStatus.cancelled:
        return 'Cancelled';
    }
  }
}

extension PaymentMethodExtension on PaymentMethod {
  String get displayName {
    switch (this) {
      case PaymentMethod.cash:
        return 'نقدي';
      case PaymentMethod.card:
        return 'بطاقة';
      case PaymentMethod.deferred:
        return 'آجل';
    }
  }

  String get displayNameEn {
    switch (this) {
      case PaymentMethod.cash:
        return 'Cash';
      case PaymentMethod.card:
        return 'Card';
      case PaymentMethod.deferred:
        return 'Deferred';
    }
  }
}
