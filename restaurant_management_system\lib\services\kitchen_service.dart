import 'dart:async';
import 'package:sqflite/sqflite.dart';
import '../models/kitchen_order.dart';
import '../models/menu_item.dart';
import '../models/order.dart';
import 'database_service.dart';

class KitchenService {
  static final KitchenService _instance = KitchenService._internal();
  factory KitchenService() => _instance;
  KitchenService._internal();

  final DatabaseService _databaseService = DatabaseService();
  
  // Stream للطلبات الجديدة
  final StreamController<KitchenOrderItem> _newOrderController = 
      StreamController<KitchenOrderItem>.broadcast();
  Stream<KitchenOrderItem> get onNewOrder => _newOrderController.stream;

  // Stream لتحديثات الحالة
  final StreamController<KitchenOrderItem> _statusUpdateController = 
      StreamController<KitchenOrderItem>.broadcast();
  Stream<KitchenOrderItem> get onStatusUpdate => _statusUpdateController.stream;

  // إنشاء جداول المطبخ
  Future<void> createKitchenTables() async {
    final db = await _databaseService.database;
    
    await db.execute('''
      CREATE TABLE IF NOT EXISTS kitchen_orders (
        id TEXT PRIMARY KEY,
        order_id TEXT NOT NULL,
        menu_item_id TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        notes TEXT,
        status TEXT NOT NULL DEFAULT 'pending',
        assigned_chef TEXT NOT NULL,
        created_at TEXT NOT NULL,
        started_at TEXT,
        completed_at TEXT,
        priority INTEGER DEFAULT 3,
        FOREIGN KEY (order_id) REFERENCES orders (id),
        FOREIGN KEY (menu_item_id) REFERENCES menu_items (id)
      )
    ''');
  }

  // إضافة طلب جديد للمطبخ
  Future<void> addOrderToKitchen(Order order) async {
    final db = await _databaseService.database;
    
    for (final item in order.items) {
      final kitchenOrderItem = KitchenOrderItem(
        id: '${order.id}_${item.menuItem.id}_${DateTime.now().millisecondsSinceEpoch}',
        orderId: order.id,
        menuItemId: item.menuItem.id!,
        menuItem: item.menuItem,
        quantity: item.quantity,
        notes: item.notes,
        assignedChef: KitchenOrderItem.getAssignedChef(item.menuItem.category),
        createdAt: DateTime.now(),
        priority: _calculatePriority(order.type, order.tableNumber),
      );

      await db.insert('kitchen_orders', kitchenOrderItem.toMap());
      
      // إرسال إشعار للشيف المناسب
      _newOrderController.add(kitchenOrderItem);
    }
  }

  // حساب أولوية الطلب
  int _calculatePriority(OrderType orderType, int? tableNumber) {
    // الطلبات السفرية لها أولوية أعلى
    if (orderType == OrderType.takeaway) return 1;
    
    // الطاولات الصغيرة لها أولوية أعلى (افتراض)
    if (tableNumber != null && tableNumber <= 5) return 2;
    
    return 3; // أولوية عادية
  }

  // الحصول على طلبات شيف معين
  Future<List<KitchenOrderItem>> getOrdersByChef(String chefType) async {
    final db = await _databaseService.database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      'kitchen_orders',
      where: 'assigned_chef = ? AND status != ?',
      whereArgs: [chefType, 'completed'],
      orderBy: 'priority ASC, created_at ASC',
    );

    final List<KitchenOrderItem> orders = [];
    
    for (final map in maps) {
      // الحصول على تفاصيل الصنف
      final menuItemMaps = await db.query(
        'menu_items',
        where: 'id = ?',
        whereArgs: [map['menu_item_id']],
      );
      
      if (menuItemMaps.isNotEmpty) {
        final menuItem = MenuItem.fromMap(menuItemMaps.first);
        orders.add(KitchenOrderItem.fromMap(map, menuItem));
      }
    }
    
    return orders;
  }

  // تحديث حالة الطلب
  Future<void> updateOrderStatus(String kitchenOrderId, KitchenOrderStatus status) async {
    final db = await _databaseService.database;
    
    final updateData = <String, dynamic>{
      'status': status.name,
    };

    // إضافة الوقت المناسب
    switch (status) {
      case KitchenOrderStatus.inProgress:
        updateData['started_at'] = DateTime.now().toIso8601String();
        break;
      case KitchenOrderStatus.ready:
      case KitchenOrderStatus.completed:
        updateData['completed_at'] = DateTime.now().toIso8601String();
        break;
      default:
        break;
    }

    await db.update(
      'kitchen_orders',
      updateData,
      where: 'id = ?',
      whereArgs: [kitchenOrderId],
    );

    // الحصول على الطلب المحدث وإرسال إشعار
    final updatedOrder = await getKitchenOrderById(kitchenOrderId);
    if (updatedOrder != null) {
      _statusUpdateController.add(updatedOrder);
    }
  }

  // الحصول على طلب مطبخ بالمعرف
  Future<KitchenOrderItem?> getKitchenOrderById(String id) async {
    final db = await _databaseService.database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      'kitchen_orders',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;

    final map = maps.first;
    
    // الحصول على تفاصيل الصنف
    final menuItemMaps = await db.query(
      'menu_items',
      where: 'id = ?',
      whereArgs: [map['menu_item_id']],
    );
    
    if (menuItemMaps.isEmpty) return null;
    
    final menuItem = MenuItem.fromMap(menuItemMaps.first);
    return KitchenOrderItem.fromMap(map, menuItem);
  }

  // الحصول على إحصائيات المطبخ
  Future<Map<String, dynamic>> getKitchenStats() async {
    final db = await _databaseService.database;
    
    // عدد الطلبات المعلقة
    final pendingResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM kitchen_orders WHERE status = ?',
      ['pending']
    );
    final pendingCount = pendingResult.first['count'] as int;

    // عدد الطلبات قيد التحضير
    final inProgressResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM kitchen_orders WHERE status = ?',
      ['inProgress']
    );
    final inProgressCount = inProgressResult.first['count'] as int;

    // عدد الطلبات الجاهزة
    final readyResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM kitchen_orders WHERE status = ?',
      ['ready']
    );
    final readyCount = readyResult.first['count'] as int;

    // متوسط وقت التحضير اليوم
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    
    final avgTimeResult = await db.rawQuery('''
      SELECT AVG(
        (julianday(completed_at) - julianday(started_at)) * 24 * 60
      ) as avg_minutes
      FROM kitchen_orders 
      WHERE status = 'completed' 
      AND started_at IS NOT NULL 
      AND completed_at IS NOT NULL
      AND created_at >= ?
    ''', [startOfDay.toIso8601String()]);
    
    final avgMinutes = avgTimeResult.first['avg_minutes'] as double? ?? 0.0;

    return {
      'pending': pendingCount,
      'inProgress': inProgressCount,
      'ready': readyCount,
      'averageTime': avgMinutes,
    };
  }

  // الحصول على الطلبات المتأخرة
  Future<List<KitchenOrderItem>> getDelayedOrders() async {
    final orders = await getAllActiveOrders();
    return orders.where((order) => order.isDelayed).toList();
  }

  // الحصول على جميع الطلبات النشطة
  Future<List<KitchenOrderItem>> getAllActiveOrders() async {
    final db = await _databaseService.database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      'kitchen_orders',
      where: 'status IN (?, ?, ?)',
      whereArgs: ['pending', 'inProgress', 'ready'],
      orderBy: 'priority ASC, created_at ASC',
    );

    final List<KitchenOrderItem> orders = [];
    
    for (final map in maps) {
      final menuItemMaps = await db.query(
        'menu_items',
        where: 'id = ?',
        whereArgs: [map['menu_item_id']],
      );
      
      if (menuItemMaps.isNotEmpty) {
        final menuItem = MenuItem.fromMap(menuItemMaps.first);
        orders.add(KitchenOrderItem.fromMap(map, menuItem));
      }
    }
    
    return orders;
  }

  // تنظيف الطلبات المكتملة القديمة
  Future<void> cleanupOldOrders() async {
    final db = await _databaseService.database;
    final threeDaysAgo = DateTime.now().subtract(const Duration(days: 3));
    
    await db.delete(
      'kitchen_orders',
      where: 'status = ? AND completed_at < ?',
      whereArgs: ['completed', threeDaysAgo.toIso8601String()],
    );
  }

  void dispose() {
    _newOrderController.close();
    _statusUpdateController.close();
  }
}
