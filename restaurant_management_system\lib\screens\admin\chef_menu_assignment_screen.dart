import 'package:flutter/material.dart';
import '../../models/chef.dart';
import '../../models/menu_item.dart';
import '../../services/restaurant_config_service.dart';
import '../../services/menu_service.dart';

class ChefMenuAssignmentScreen extends StatefulWidget {
  const ChefMenuAssignmentScreen({super.key});

  @override
  State<ChefMenuAssignmentScreen> createState() => _ChefMenuAssignmentScreenState();
}

class _ChefMenuAssignmentScreenState extends State<ChefMenuAssignmentScreen> {
  final RestaurantConfigService _configService = RestaurantConfigService();
  final MenuService _menuService = MenuService();
  
  List<Chef> _chefs = [];
  List<MenuItem> _menuItems = [];
  Map<String, List<String>> _chefCategories = {}; // chef_id -> categories
  Map<String, List<String>> _chefMenuItems = {}; // chef_id -> menu_item_ids
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      final chefs = await _configService.getAllChefs();
      final menuItems = await _menuService.getAllMenuItems();
      
      // تحميل التخصيصات الحالية
      final Map<String, List<String>> chefCategories = {};
      final Map<String, List<String>> chefMenuItems = {};
      
      for (final chef in chefs) {
        chefCategories[chef.id] = chef.assignedCategories;
        chefMenuItems[chef.id] = chef.assignedMenuItems;
      }
      
      setState(() {
        _chefs = chefs;
        _menuItems = menuItems;
        _chefCategories = chefCategories;
        _chefMenuItems = chefMenuItems;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showError('خطأ في تحميل البيانات: $e');
    }
  }

  Future<void> _saveAssignments() async {
    try {
      for (final chef in _chefs) {
        final updatedChef = chef.copyWith(
          assignedCategories: _chefCategories[chef.id] ?? [],
          assignedMenuItems: _chefMenuItems[chef.id] ?? [],
        );
        await _configService.updateChef(updatedChef);
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ التخصيصات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      _showError('خطأ في حفظ التخصيصات: $e');
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  // الحصول على المجموعات الفريدة
  List<String> get _uniqueCategories {
    return _menuItems.map((item) => item.category).toSet().toList()..sort();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'ربط الأصناف بالشيفات',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _saveAssignments,
            icon: const Icon(Icons.save),
          ),
          IconButton(
            onPressed: _loadData,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : DefaultTabController(
              length: 2,
              child: Column(
                children: [
                  const TabBar(
                    tabs: [
                      Tab(text: 'ربط المجموعات', icon: Icon(Icons.category)),
                      Tab(text: 'ربط الأصناف', icon: Icon(Icons.restaurant_menu)),
                    ],
                  ),
                  Expanded(
                    child: TabBarView(
                      children: [
                        _buildCategoriesTab(),
                        _buildMenuItemsTab(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildCategoriesTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _uniqueCategories.length,
      itemBuilder: (context, index) {
        final category = _uniqueCategories[index];
        return _buildCategoryCard(category);
      },
    );
  }

  Widget _buildCategoryCard(String category) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ExpansionTile(
        title: Text(
          category,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          'عدد الأصناف: ${_menuItems.where((item) => item.category == category).length}',
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'اختر الشيفات المسؤولين عن هذه المجموعة:',
                  style: TextStyle(fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  children: _chefs.map((chef) {
                    final isAssigned = _chefCategories[chef.id]?.contains(category) ?? false;
                    return FilterChip(
                      label: Text(chef.name),
                      selected: isAssigned,
                      selectedColor: Color(int.parse(chef.color.substring(1), radix: 16) + 0xFF000000).withOpacity(0.3),
                      onSelected: (selected) {
                        setState(() {
                          if (selected) {
                            _chefCategories[chef.id] = (_chefCategories[chef.id] ?? [])..add(category);
                          } else {
                            _chefCategories[chef.id]?.remove(category);
                          }
                        });
                      },
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItemsTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _menuItems.length,
      itemBuilder: (context, index) {
        final menuItem = _menuItems[index];
        return _buildMenuItemCard(menuItem);
      },
    );
  }

  Widget _buildMenuItemCard(MenuItem menuItem) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: menuItem.imageUrl != null
            ? ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  menuItem.imageUrl!,
                  width: 50,
                  height: 50,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(Icons.restaurant),
                    );
                  },
                ),
              )
            : Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.restaurant),
              ),
        title: Text(menuItem.name),
        subtitle: Text('${menuItem.category} - ${menuItem.price} ريال'),
        trailing: SizedBox(
          width: 200,
          child: Wrap(
            spacing: 4,
            children: _chefs.map((chef) {
              final isAssigned = _chefMenuItems[chef.id]?.contains(menuItem.id) ?? false;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    if (isAssigned) {
                      _chefMenuItems[chef.id]?.remove(menuItem.id);
                    } else {
                      _chefMenuItems[chef.id] = (_chefMenuItems[chef.id] ?? [])..add(menuItem.id);
                    }
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: isAssigned 
                        ? Color(int.parse(chef.color.substring(1), radix: 16) + 0xFF000000)
                        : Colors.grey[300],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    chef.name.split(' ').first,
                    style: TextStyle(
                      color: isAssigned ? Colors.white : Colors.black,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }
}
