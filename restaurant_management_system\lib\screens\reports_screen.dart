import 'package:flutter/material.dart';
import 'package:restaurant_management_system/models/order.dart';
import '../models/bill.dart';
import '../services/bill_service.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> {
  final BillService _billService = BillService();
  DateTime _selectedDate = DateTime.now();
  DailySales? _dailySales;
  List<Bill> _todayBills = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadReports();
  }

  Future<void> _loadReports() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final dailySales = await _billService.getDailySales(_selectedDate);
      final bills = await _billService.getBillsByDate(_selectedDate);
      
      setState(() {
        _dailySales = dailySales;
        _todayBills = bills;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل التقارير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
      _loadReports();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير'),
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: _selectDate,
            tooltip: 'اختيار التاريخ',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadReports,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // تاريخ التقرير
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          const Icon(Icons.calendar_today),
                          const SizedBox(width: 16),
                          Text(
                            'تقرير يوم ${_formatDate(_selectedDate)}',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          ElevatedButton(
                            onPressed: _selectDate,
                            child: const Text('تغيير التاريخ'),
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // إحصائيات المبيعات
                  if (_dailySales != null) ...[
                    const Text(
                      'ملخص المبيعات',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                      childAspectRatio: 1.5,
                      children: [
                        _buildStatCard(
                          'إجمالي الطلبات',
                          _dailySales!.totalOrders.toString(),
                          Icons.receipt_long,
                          Colors.blue,
                        ),
                        _buildStatCard(
                          'إجمالي المبيعات',
                          '${_dailySales!.totalSales.toStringAsFixed(2)} ريال',
                          Icons.attach_money,
                          Colors.green,
                        ),
                        _buildStatCard(
                          'المبيعات النقدية',
                          '${_dailySales!.cashSales.toStringAsFixed(2)} ريال',
                          Icons.money,
                          Colors.orange,
                        ),
                        _buildStatCard(
                          'مبيعات البطاقات',
                          '${_dailySales!.cardSales.toStringAsFixed(2)} ريال',
                          Icons.credit_card,
                          Colors.purple,
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // تفاصيل إضافية
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'تفاصيل إضافية',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),
                            _buildDetailRow(
                              'ضريبة القيمة المضافة',
                              '${_dailySales!.totalTax.toStringAsFixed(2)} ريال',
                            ),
                            _buildDetailRow(
                              'إجمالي الخصومات',
                              '${_dailySales!.totalDiscount.toStringAsFixed(2)} ريال',
                            ),
                            _buildDetailRow(
                              'المبيعات الآجلة',
                              '${_dailySales!.deferredSales.toStringAsFixed(2)} ريال',
                            ),
                            const Divider(),
                            _buildDetailRow(
                              'متوسط قيمة الطلب',
                              _dailySales!.totalOrders > 0
                                  ? '${(_dailySales!.totalSales / _dailySales!.totalOrders).toStringAsFixed(2)} ريال'
                                  : '0.00 ريال',
                              isTotal: true,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                  
                  const SizedBox(height: 24),
                  
                  // قائمة الفواتير
                  Row(
                    children: [
                      const Text(
                        'فواتير اليوم',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '${_todayBills.length} فاتورة',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  
                  if (_todayBills.isEmpty)
                    const Card(
                      child: Padding(
                        padding: EdgeInsets.all(32),
                        child: Center(
                          child: Text(
                            'لا توجد فواتير لهذا اليوم',
                            style: TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                    )
                  else
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _todayBills.length,
                      itemBuilder: (context, index) {
                        final bill = _todayBills[index];
                        return _buildBillCard(bill);
                      },
                    ),
                ],
              ),
            ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: isTotal ? 16 : 14,
                fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: FontWeight.bold,
              color: isTotal ? Colors.green : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBillCard(Bill bill) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Colors.green[100],
          child: Icon(
            Icons.receipt,
            color: Colors.green[700],
          ),
        ),
        title: Text('فاتورة رقم ${bill.billNumber}'),
        subtitle: Text(
          '${bill.paymentMethod.displayName} - ${_formatTime(bill.createdAt)}',
        ),
        trailing: Text(
          '${bill.totalAmount.toStringAsFixed(2)} ريال',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.green,
          ),
        ),
        onTap: () {
          // يمكن إضافة عرض تفاصيل الفاتورة هنا
        },
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
