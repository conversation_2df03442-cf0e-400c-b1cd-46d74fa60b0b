import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/menu_item.dart';

class AddMenuItemDialog extends StatefulWidget {
  final MenuItem? menuItem;

  const AddMenuItemDialog({super.key, this.menuItem});

  @override
  State<AddMenuItemDialog> createState() => _AddMenuItemDialogState();
}

class _AddMenuItemDialogState extends State<AddMenuItemDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _nameEnController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _descriptionEnController = TextEditingController();
  final _priceController = TextEditingController();
  final _imageUrlController = TextEditingController();

  String _selectedCategory = MenuCategories.mainCourses;
  bool _isAvailable = true;

  @override
  void initState() {
    super.initState();
    if (widget.menuItem != null) {
      _nameController.text = widget.menuItem!.name;
      _nameEnController.text = widget.menuItem!.nameEn;
      _descriptionController.text = widget.menuItem!.description;
      _descriptionEnController.text = widget.menuItem!.descriptionEn;
      _priceController.text = widget.menuItem!.price.toString();
      _imageUrlController.text = widget.menuItem!.imagePath ?? '';
      _selectedCategory = widget.menuItem!.category;
      _isAvailable = widget.menuItem!.isAvailable;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _nameEnController.dispose();
    _descriptionController.dispose();
    _descriptionEnController.dispose();
    _priceController.dispose();
    _imageUrlController.dispose();
    super.dispose();
  }

  void _saveMenuItem() {
    if (_formKey.currentState!.validate()) {
      final menuItem = MenuItem(
        id: widget.menuItem?.id,
        name: _nameController.text.trim(),
        nameEn: _nameEnController.text.trim(),
        description: _descriptionController.text.trim(),
        descriptionEn: _descriptionEnController.text.trim(),
        price: double.parse(_priceController.text),
        category: _selectedCategory,
        imagePath: _imageUrlController.text.trim().isNotEmpty
            ? _imageUrlController.text.trim()
            : null,
        isAvailable: _isAvailable,
        createdAt: widget.menuItem?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      Navigator.of(context).pop(menuItem);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 500),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // العنوان
                  Text(
                    widget.menuItem == null ? 'إضافة صنف جديد' : 'تعديل الصنف',
                    style: Theme.of(context).textTheme.headlineSmall,
                    textDirection: TextDirection.rtl,
                  ),
                  const SizedBox(height: 24),

                  // اسم الصنف بالعربية
                  TextFormField(
                    controller: _nameController,
                    decoration: const InputDecoration(
                      labelText: 'اسم الصنف (عربي)',
                      border: OutlineInputBorder(),
                    ),
                    textDirection: TextDirection.rtl,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال اسم الصنف';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // اسم الصنف بالإنجليزية
                  TextFormField(
                    controller: _nameEnController,
                    decoration: const InputDecoration(
                      labelText: 'اسم الصنف (English)',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال اسم الصنف بالإنجليزية';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // الوصف بالعربية
                  TextFormField(
                    controller: _descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'الوصف (عربي)',
                      border: OutlineInputBorder(),
                    ),
                    textDirection: TextDirection.rtl,
                    maxLines: 3,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال وصف الصنف';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // الوصف بالإنجليزية
                  TextFormField(
                    controller: _descriptionEnController,
                    decoration: const InputDecoration(
                      labelText: 'الوصف (English)',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال وصف الصنف بالإنجليزية';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // رابط الصورة
                  TextFormField(
                    controller: _imageUrlController,
                    decoration: const InputDecoration(
                      labelText: 'رابط الصورة (اختياري)',
                      hintText: 'https://example.com/image.jpg',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.image),
                    ),
                    validator: (value) {
                      if (value != null && value.trim().isNotEmpty) {
                        final uri = Uri.tryParse(value.trim());
                        if (uri == null || !uri.hasScheme) {
                          return 'يرجى إدخال رابط صحيح للصورة';
                        }
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // السعر
                  TextFormField(
                    controller: _priceController,
                    decoration: const InputDecoration(
                      labelText: 'السعر (ريال)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.attach_money),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(
                          RegExp(r'^\d+\.?\d{0,2}')),
                    ],
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال السعر';
                      }
                      final price = double.tryParse(value);
                      if (price == null || price <= 0) {
                        return 'يرجى إدخال سعر صحيح';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // الفئة
                  DropdownButtonFormField<String>(
                    value: _selectedCategory,
                    decoration: const InputDecoration(
                      labelText: 'الفئة',
                      border: OutlineInputBorder(),
                    ),
                    items: MenuCategories.allCategories.map((category) {
                      return DropdownMenuItem(
                        value: category,
                        child: Text(
                          category,
                          textDirection: TextDirection.rtl,
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedCategory = value!;
                      });
                    },
                  ),
                  const SizedBox(height: 16),

                  // حالة التوفر
                  Row(
                    children: [
                      Checkbox(
                        value: _isAvailable,
                        onChanged: (value) {
                          setState(() {
                            _isAvailable = value!;
                          });
                        },
                      ),
                      const Text(
                        'متوفر للطلب',
                        style: TextStyle(fontSize: 16),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // أزرار الإجراءات
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('إلغاء'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _saveMenuItem,
                          child: Text(
                            widget.menuItem == null ? 'إضافة' : 'حفظ',
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
