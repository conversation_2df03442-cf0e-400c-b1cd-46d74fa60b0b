import 'package:flutter/material.dart';
import '../models/menu_item.dart';
import '../models/order.dart';
import '../services/menu_service.dart';
import '../services/order_service.dart';
import '../services/kitchen_service.dart';
import '../widgets/order_item_widget.dart';
import '../widgets/pos_menu_item_card.dart';

class CreateOrderScreen extends StatefulWidget {
  const CreateOrderScreen({super.key});

  @override
  State<CreateOrderScreen> createState() => _CreateOrderScreenState();
}

class _CreateOrderScreenState extends State<CreateOrderScreen> {
  final MenuService _menuService = MenuService();
  final OrderService _orderService = OrderService();
  final KitchenService _kitchenService = KitchenService();

  List<MenuItem> _menuItems = [];
  final List<OrderItem> _orderItems = [];
  List<String> _categories = [];
  String _selectedCategory = 'الكل';
  OrderType _orderType = OrderType.dineIn;
  int? _tableNumber;
  bool _isLoading = true;

  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _customerNameController = TextEditingController();
  final TextEditingController _customerPhoneController =
      TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadMenuItems();
    _loadCategories();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _customerNameController.dispose();
    _customerPhoneController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadMenuItems() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final items = await _menuService.getAvailableMenuItems();
      setState(() {
        _menuItems = items;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل القائمة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await _menuService.getAvailableCategories();
      setState(() {
        _categories = ['الكل', ...categories];
      });
    } catch (e) {
      print('خطأ في تحميل الفئات: $e');
    }
  }

  List<MenuItem> get _filteredMenuItems {
    List<MenuItem> filtered = _menuItems;

    if (_selectedCategory != 'الكل') {
      filtered =
          filtered.where((item) => item.category == _selectedCategory).toList();
    }

    final searchQuery = _searchController.text.toLowerCase();
    if (searchQuery.isNotEmpty) {
      filtered = filtered
          .where((item) =>
              item.name.toLowerCase().contains(searchQuery) ||
              item.nameEn.toLowerCase().contains(searchQuery))
          .toList();
    }

    return filtered;
  }

  void _addToOrder(MenuItem menuItem) {
    setState(() {
      final existingIndex = _orderItems.indexWhere(
        (item) => item.menuItemId == menuItem.id,
      );

      if (existingIndex >= 0) {
        _orderItems[existingIndex] = _orderItems[existingIndex].copyWith(
          quantity: _orderItems[existingIndex].quantity + 1,
        );
      } else {
        _orderItems.add(OrderItem(
          menuItemId: menuItem.id!,
          menuItem: menuItem,
          quantity: 1,
          unitPrice: menuItem.price,
        ));
      }
    });
  }

  void _removeFromOrder(int index) {
    setState(() {
      _orderItems.removeAt(index);
    });
  }

  void _updateQuantity(int index, int quantity) {
    if (quantity <= 0) {
      _removeFromOrder(index);
    } else {
      setState(() {
        _orderItems[index] = _orderItems[index].copyWith(quantity: quantity);
      });
    }
  }

  double get _subtotal {
    return _orderItems.fold(0.0, (sum, item) => sum + item.totalPrice);
  }

  double get _taxAmount {
    return _subtotal * 0.15; // 15% ضريبة القيمة المضافة
  }

  double get _totalAmount {
    return _subtotal + _taxAmount;
  }

  Future<void> _createOrder() async {
    if (_orderItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إضافة أصناف للطلب'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_orderType == OrderType.dineIn && _tableNumber == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار رقم الطاولة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      final orderNumber = await _orderService.generateOrderNumber();

      final order = Order(
        id: 'order_${DateTime.now().millisecondsSinceEpoch}',
        orderNumber: orderNumber,
        type: _orderType,
        tableNumber: _tableNumber,
        customerName: _customerNameController.text.trim().isNotEmpty
            ? _customerNameController.text.trim()
            : null,
        customerPhone: _customerPhoneController.text.trim().isNotEmpty
            ? _customerPhoneController.text.trim()
            : null,
        items: _orderItems,
        subtotal: _subtotal,
        taxAmount: _taxAmount,
        totalAmount: _totalAmount,
        notes: _notesController.text.trim().isNotEmpty
            ? _notesController.text.trim()
            : null,
      );

      await _orderService.createOrder(order);

      // إرسال الطلب للمطبخ
      await _kitchenService.addOrderToKitchen(order);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('تم إنشاء الطلب رقم $orderNumber وإرساله للمطبخ بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء الطلب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('طلب جديد'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _createOrder,
            tooltip: 'حفظ الطلب',
          ),
        ],
      ),
      body: Row(
        children: [
          // قائمة الأصناف
          Expanded(
            flex: 2,
            child: Column(
              children: [
                // شريط البحث والفلتر
                Container(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      TextField(
                        controller: _searchController,
                        decoration: const InputDecoration(
                          hintText: 'البحث في القائمة...',
                          prefixIcon: Icon(Icons.search),
                          border: OutlineInputBorder(),
                        ),
                        onChanged: (value) => setState(() {}),
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        height: 40,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: _categories.length,
                          itemBuilder: (context, index) {
                            final category = _categories[index];
                            final isSelected = category == _selectedCategory;

                            return Padding(
                              padding: const EdgeInsets.only(right: 8),
                              child: FilterChip(
                                label: Text(category),
                                selected: isSelected,
                                onSelected: (selected) {
                                  setState(() {
                                    _selectedCategory = category;
                                  });
                                },
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),

                // قائمة الأصناف
                Expanded(
                  child: _isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : GridView.builder(
                          padding: const EdgeInsets.all(16),
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            crossAxisSpacing: 12,
                            mainAxisSpacing: 12,
                            childAspectRatio: 0.75,
                          ),
                          itemCount: _filteredMenuItems.length,
                          itemBuilder: (context, index) {
                            final item = _filteredMenuItems[index];
                            return POSMenuItemCard(
                              menuItem: item,
                              onTap: () => _addToOrder(item),
                              isSelected: _orderItems.any((orderItem) =>
                                  orderItem.menuItemId == item.id),
                            );
                          },
                        ),
                ),
              ],
            ),
          ),

          // الطلب الحالي
          Expanded(
            flex: 1,
            child: Container(
              decoration: BoxDecoration(
                border: Border(
                  left: BorderSide(color: Colors.grey[300]!),
                ),
              ),
              child: Column(
                children: [
                  // معلومات الطلب
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'تفاصيل الطلب',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // نوع الطلب
                        Column(
                          children: [
                            Row(
                              children: [
                                Radio<OrderType>(
                                  value: OrderType.dineIn,
                                  groupValue: _orderType,
                                  onChanged: (value) {
                                    setState(() {
                                      _orderType = value!;
                                    });
                                  },
                                ),
                                const Text('داخل المطعم'),
                                const SizedBox(width: 20),
                                Radio<OrderType>(
                                  value: OrderType.takeaway,
                                  groupValue: _orderType,
                                  onChanged: (value) {
                                    setState(() {
                                      _orderType = value!;
                                    });
                                  },
                                ),
                                const Text('سفري'),
                              ],
                            ),
                          ],
                        ),

                        // رقم الطاولة (إذا كان داخل المطعم)
                        if (_orderType == OrderType.dineIn) ...[
                          const SizedBox(height: 8),
                          DropdownButtonFormField<int>(
                            value: _tableNumber,
                            decoration: const InputDecoration(
                              labelText: 'رقم الطاولة',
                              border: OutlineInputBorder(),
                            ),
                            items: List.generate(15, (index) => index + 1)
                                .map((number) => DropdownMenuItem(
                                      value: number,
                                      child: Text('طاولة $number'),
                                    ))
                                .toList(),
                            onChanged: (value) {
                              setState(() {
                                _tableNumber = value;
                              });
                            },
                          ),
                        ],

                        // معلومات العميل (للسفري)
                        if (_orderType == OrderType.takeaway) ...[
                          const SizedBox(height: 8),
                          TextField(
                            controller: _customerNameController,
                            decoration: const InputDecoration(
                              labelText: 'اسم العميل',
                              border: OutlineInputBorder(),
                            ),
                          ),
                          const SizedBox(height: 8),
                          TextField(
                            controller: _customerPhoneController,
                            decoration: const InputDecoration(
                              labelText: 'رقم الهاتف',
                              border: OutlineInputBorder(),
                            ),
                            keyboardType: TextInputType.phone,
                          ),
                        ],

                        const SizedBox(height: 8),
                        TextField(
                          controller: _notesController,
                          decoration: const InputDecoration(
                            labelText: 'ملاحظات',
                            border: OutlineInputBorder(),
                          ),
                          maxLines: 2,
                        ),
                      ],
                    ),
                  ),

                  // قائمة أصناف الطلب
                  Expanded(
                    child: _orderItems.isEmpty
                        ? const Center(
                            child: Text(
                              'لم يتم إضافة أصناف بعد',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey,
                              ),
                            ),
                          )
                        : ListView.builder(
                            padding: const EdgeInsets.all(16),
                            itemCount: _orderItems.length,
                            itemBuilder: (context, index) {
                              final orderItem = _orderItems[index];
                              return OrderItemWidget(
                                orderItem: orderItem,
                                onQuantityChanged: (quantity) =>
                                    _updateQuantity(index, quantity),
                                onRemove: () => _removeFromOrder(index),
                              );
                            },
                          ),
                  ),

                  // ملخص الطلب
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(color: Colors.grey[300]!),
                      ),
                    ),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('المجموع الفرعي:'),
                            Text('${_subtotal.toStringAsFixed(2)} ريال'),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('ضريبة القيمة المضافة (15%):'),
                            Text('${_taxAmount.toStringAsFixed(2)} ريال'),
                          ],
                        ),
                        const Divider(),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'المجموع الكلي:',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '${_totalAmount.toStringAsFixed(2)} ريال',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: _createOrder,
                            child: const Text('إنشاء الطلب'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
