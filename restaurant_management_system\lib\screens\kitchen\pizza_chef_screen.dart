import 'package:flutter/material.dart';
import '../../models/kitchen_order.dart';
import '../../models/user_role.dart';
import '../../widgets/kitchen/kitchen_order_card.dart';
import '../../services/kitchen_service.dart';
import '../../services/audio_service.dart';

class PizzaChefScreen extends StatefulWidget {
  const PizzaChefScreen({super.key});

  @override
  State<PizzaChefScreen> createState() => _PizzaChefScreenState();
}

class _PizzaChefScreenState extends State<PizzaChefScreen> {
  final KitchenService _kitchenService = KitchenService();
  final AudioService _audioService = AudioService();
  List<KitchenOrderItem> _pizzaOrders = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPizzaOrders();
    _setupOrderListener();
  }

  void _loadPizzaOrders() async {
    setState(() => _isLoading = true);
    try {
      final orders = await _kitchenService.getOrdersByChef('pizza_chef');
      setState(() {
        _pizzaOrders = orders;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showError('خطأ في تحميل الطلبات: $e');
    }
  }

  void _setupOrderListener() {
    // الاستماع للطلبات الجديدة
    _kitchenService.onNewOrder.listen((order) {
      if (order.assignedChef == 'pizza_chef') {
        setState(() {
          _pizzaOrders.insert(0, order);
        });
        _audioService.playNewOrderSound();
        _showNewOrderNotification(order);
      }
    });
  }

  void _showNewOrderNotification(KitchenOrderItem order) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.local_pizza, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'طلب بيتزا جديد: ${order.menuItem.name} x${order.quantity}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'عرض',
          textColor: Colors.white,
          onPressed: () => _scrollToOrder(order.id),
        ),
      ),
    );
  }

  void _scrollToOrder(String orderId) {
    // تنفيذ التمرير للطلب المحدد
  }

  void _updateOrderStatus(KitchenOrderItem order, KitchenOrderStatus newStatus) async {
    try {
      await _kitchenService.updateOrderStatus(order.id, newStatus);
      setState(() {
        final index = _pizzaOrders.indexWhere((o) => o.id == order.id);
        if (index != -1) {
          _pizzaOrders[index] = order.copyWith(
            status: newStatus,
            startedAt: newStatus == KitchenOrderStatus.inProgress 
                ? DateTime.now() 
                : order.startedAt,
            completedAt: newStatus == KitchenOrderStatus.ready 
                ? DateTime.now() 
                : order.completedAt,
          );
        }
      });

      // تشغيل صوت عند اكتمال الطلب
      if (newStatus == KitchenOrderStatus.ready) {
        _audioService.playOrderReadySound();
      }
    } catch (e) {
      _showError('خطأ في تحديث حالة الطلب: $e');
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Row(
          children: [
            Icon(Icons.local_pizza, color: Colors.white),
            SizedBox(width: 8),
            Text(
              'شيف البيتزا',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ],
        ),
        backgroundColor: Colors.orange,
        actions: [
          // عداد الطلبات المعلقة
          Container(
            margin: const EdgeInsets.only(right: 16),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '${_pizzaOrders.where((o) => o.status != KitchenOrderStatus.completed).length}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ),
                ),
                const SizedBox(width: 4),
                const Text(
                  'طلب',
                  style: TextStyle(color: Colors.orange),
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPizzaOrders,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildOrdersList(),
    );
  }

  Widget _buildOrdersList() {
    if (_pizzaOrders.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.local_pizza,
              size: 80,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد طلبات بيتزا حالياً',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    // تجميع الطلبات حسب الحالة
    final pendingOrders = _pizzaOrders
        .where((o) => o.status == KitchenOrderStatus.pending)
        .toList();
    final inProgressOrders = _pizzaOrders
        .where((o) => o.status == KitchenOrderStatus.inProgress)
        .toList();
    final readyOrders = _pizzaOrders
        .where((o) => o.status == KitchenOrderStatus.ready)
        .toList();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الطلبات الجديدة
          if (pendingOrders.isNotEmpty) ...[
            _buildSectionHeader('طلبات جديدة', pendingOrders.length, Colors.blue),
            const SizedBox(height: 8),
            ...pendingOrders.map((order) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: KitchenOrderCard(
                order: order,
                onStatusUpdate: _updateOrderStatus,
              ),
            )),
            const SizedBox(height: 24),
          ],

          // الطلبات قيد التحضير
          if (inProgressOrders.isNotEmpty) ...[
            _buildSectionHeader('قيد التحضير', inProgressOrders.length, Colors.orange),
            const SizedBox(height: 8),
            ...inProgressOrders.map((order) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: KitchenOrderCard(
                order: order,
                onStatusUpdate: _updateOrderStatus,
              ),
            )),
            const SizedBox(height: 24),
          ],

          // الطلبات الجاهزة
          if (readyOrders.isNotEmpty) ...[
            _buildSectionHeader('جاهز للتقديم', readyOrders.length, Colors.green),
            const SizedBox(height: 8),
            ...readyOrders.map((order) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: KitchenOrderCard(
                order: order,
                onStatusUpdate: _updateOrderStatus,
              ),
            )),
          ],
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, int count, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              count.toString(),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
