import 'order.dart';

class Bill {
  final int? id;
  final String billNumber;
  final int orderId;
  final Order? order;
  final double subtotal;
  final double taxAmount;
  final double discountAmount;
  final double totalAmount;
  final double paidAmount;
  final double changeAmount;
  final PaymentMethod paymentMethod;
  final String? paymentReference;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  Bill({
    this.id,
    required this.billNumber,
    required this.orderId,
    this.order,
    required this.subtotal,
    this.taxAmount = 0.0,
    this.discountAmount = 0.0,
    required this.totalAmount,
    required this.paidAmount,
    this.changeAmount = 0.0,
    required this.paymentMethod,
    this.paymentReference,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  factory Bill.fromMap(Map<String, dynamic> map) {
    return Bill(
      id: map['id'],
      billNumber: map['bill_number'] ?? '',
      orderId: map['order_id'] ?? 0,
      subtotal: (map['subtotal'] ?? 0.0).toDouble(),
      taxAmount: (map['tax_amount'] ?? 0.0).toDouble(),
      discountAmount: (map['discount_amount'] ?? 0.0).toDouble(),
      totalAmount: (map['total_amount'] ?? 0.0).toDouble(),
      paidAmount: (map['paid_amount'] ?? 0.0).toDouble(),
      changeAmount: (map['change_amount'] ?? 0.0).toDouble(),
      paymentMethod: PaymentMethod.values[map['payment_method'] ?? 0],
      paymentReference: map['payment_reference'],
      notes: map['notes'],
      createdAt: DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(map['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'bill_number': billNumber,
      'order_id': orderId,
      'subtotal': subtotal,
      'tax_amount': taxAmount,
      'discount_amount': discountAmount,
      'total_amount': totalAmount,
      'paid_amount': paidAmount,
      'change_amount': changeAmount,
      'payment_method': paymentMethod.index,
      'payment_reference': paymentReference,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Bill copyWith({
    int? id,
    String? billNumber,
    int? orderId,
    Order? order,
    double? subtotal,
    double? taxAmount,
    double? discountAmount,
    double? totalAmount,
    double? paidAmount,
    double? changeAmount,
    PaymentMethod? paymentMethod,
    String? paymentReference,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Bill(
      id: id ?? this.id,
      billNumber: billNumber ?? this.billNumber,
      orderId: orderId ?? this.orderId,
      order: order ?? this.order,
      subtotal: subtotal ?? this.subtotal,
      taxAmount: taxAmount ?? this.taxAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      paidAmount: paidAmount ?? this.paidAmount,
      changeAmount: changeAmount ?? this.changeAmount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentReference: paymentReference ?? this.paymentReference,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Bill{id: $id, billNumber: $billNumber, totalAmount: $totalAmount, paymentMethod: $paymentMethod}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Bill && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// نموذج للتقارير المالية
class DailySales {
  final DateTime date;
  final int totalOrders;
  final double totalSales;
  final double cashSales;
  final double cardSales;
  final double deferredSales;
  final double totalTax;
  final double totalDiscount;

  DailySales({
    required this.date,
    required this.totalOrders,
    required this.totalSales,
    required this.cashSales,
    required this.cardSales,
    required this.deferredSales,
    required this.totalTax,
    required this.totalDiscount,
  });

  factory DailySales.fromMap(Map<String, dynamic> map) {
    return DailySales(
      date: DateTime.parse(map['date']),
      totalOrders: map['total_orders'] ?? 0,
      totalSales: (map['total_sales'] ?? 0.0).toDouble(),
      cashSales: (map['cash_sales'] ?? 0.0).toDouble(),
      cardSales: (map['card_sales'] ?? 0.0).toDouble(),
      deferredSales: (map['deferred_sales'] ?? 0.0).toDouble(),
      totalTax: (map['total_tax'] ?? 0.0).toDouble(),
      totalDiscount: (map['total_discount'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'date': date.toIso8601String().split('T')[0],
      'total_orders': totalOrders,
      'total_sales': totalSales,
      'cash_sales': cashSales,
      'card_sales': cardSales,
      'deferred_sales': deferredSales,
      'total_tax': totalTax,
      'total_discount': totalDiscount,
    };
  }
}
