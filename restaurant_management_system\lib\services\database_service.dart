import 'dart:async';
import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, 'restaurant_management.db');

    return await openDatabase(
      path,
      version: 3,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // جدول أصناف الطعام
    await db.execute('''
      CREATE TABLE menu_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        name_en TEXT NOT NULL,
        description TEXT,
        description_en TEXT,
        price REAL NOT NULL,
        category TEXT NOT NULL,
        image_path TEXT,
        is_available INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // جدول الطاولات
    await db.execute('''
      CREATE TABLE tables (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        number INTEGER UNIQUE NOT NULL,
        capacity INTEGER NOT NULL,
        status INTEGER DEFAULT 0,
        location TEXT,
        notes TEXT,
        reservation_time TEXT,
        reserved_by TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // جدول الطلبات
    await db.execute('''
      CREATE TABLE orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_number TEXT UNIQUE NOT NULL,
        type INTEGER NOT NULL,
        status INTEGER DEFAULT 0,
        table_number INTEGER,
        customer_name TEXT,
        customer_phone TEXT,
        subtotal REAL NOT NULL,
        tax_amount REAL DEFAULT 0,
        discount_amount REAL DEFAULT 0,
        total_amount REAL NOT NULL,
        payment_method INTEGER,
        is_paid INTEGER DEFAULT 0,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (table_number) REFERENCES tables (number)
      )
    ''');

    // جدول عناصر الطلب
    await db.execute('''
      CREATE TABLE order_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER NOT NULL,
        menu_item_id INTEGER NOT NULL,
        quantity INTEGER NOT NULL,
        unit_price REAL NOT NULL,
        notes TEXT,
        FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE,
        FOREIGN KEY (menu_item_id) REFERENCES menu_items (id)
      )
    ''');

    // جدول الفواتير
    await db.execute('''
      CREATE TABLE bills (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        bill_number TEXT UNIQUE NOT NULL,
        order_id INTEGER NOT NULL,
        subtotal REAL NOT NULL,
        tax_amount REAL DEFAULT 0,
        discount_amount REAL DEFAULT 0,
        total_amount REAL NOT NULL,
        paid_amount REAL NOT NULL,
        change_amount REAL DEFAULT 0,
        payment_method INTEGER NOT NULL,
        payment_reference TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (order_id) REFERENCES orders (id)
      )
    ''');

    // جدول الإعدادات
    await db.execute('''
      CREATE TABLE settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE NOT NULL,
        value TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // إدراج بيانات أولية
    await _insertInitialData(db);

    // إنشاء جداول الإعدادات المرنة
    await _createConfigTables(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      await _createConfigTables(db);
    }
    if (oldVersion < 3) {
      await _upgradeChefTable(db);
    }
  }

  // ترقية جدول الشيفات لإضافة الحقول الجديدة
  Future<void> _upgradeChefTable(Database db) async {
    try {
      // إضافة عمود كلمة المرور
      await db.execute(
          'ALTER TABLE chefs ADD COLUMN password TEXT DEFAULT "123456"');
    } catch (e) {
      // العمود موجود بالفعل
    }

    try {
      // إضافة عمود المجموعات المخصصة
      await db.execute(
          'ALTER TABLE chefs ADD COLUMN assigned_categories TEXT DEFAULT ""');
    } catch (e) {
      // العمود موجود بالفعل
    }

    try {
      // إضافة عمود الأصناف المخصصة
      await db.execute(
          'ALTER TABLE chefs ADD COLUMN assigned_menu_items TEXT DEFAULT ""');
    } catch (e) {
      // العمود موجود بالفعل
    }
  }

  // إنشاء جداول الإعدادات المرنة
  Future<void> _createConfigTables(Database db) async {
    // جدول إعدادات المطعم
    await db.execute('''
      CREATE TABLE IF NOT EXISTS restaurant_config (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        logo TEXT,
        address TEXT NOT NULL,
        phone TEXT NOT NULL,
        email TEXT NOT NULL,
        working_hours TEXT NOT NULL,
        service_types TEXT NOT NULL,
        currency TEXT DEFAULT 'ريال',
        tax_rate REAL DEFAULT 0.15,
        service_charge REAL DEFAULT 0.0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // جدول أقسام الطاولات
    await db.execute('''
      CREATE TABLE IF NOT EXISTS table_sections (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        max_tables INTEGER DEFAULT 50,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL
      )
    ''');

    // جدول الطاولات المرنة
    await db.execute('''
      CREATE TABLE IF NOT EXISTS restaurant_tables (
        id TEXT PRIMARY KEY,
        number TEXT NOT NULL,
        section TEXT DEFAULT 'القاعة الرئيسية',
        capacity INTEGER NOT NULL,
        type TEXT DEFAULT 'regular',
        status TEXT DEFAULT 'available',
        position_x REAL,
        position_y REAL,
        notes TEXT,
        created_at TEXT NOT NULL,
        last_updated TEXT
      )
    ''');

    // جدول الشيفات
    await db.execute('''
      CREATE TABLE IF NOT EXISTS chefs (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        username TEXT UNIQUE NOT NULL,
        password TEXT DEFAULT '123456',
        specialty TEXT NOT NULL,
        description TEXT,
        color TEXT NOT NULL,
        status TEXT DEFAULT 'active',
        working_days TEXT NOT NULL,
        start_time TEXT NOT NULL,
        end_time TEXT NOT NULL,
        assigned_categories TEXT DEFAULT '',
        assigned_menu_items TEXT DEFAULT '',
        device_id TEXT,
        receive_notifications INTEGER DEFAULT 1,
        priority INTEGER DEFAULT 5,
        created_at TEXT NOT NULL,
        last_login TEXT
      )
    ''');

    // جدول ربط الشيفات بالأصناف
    await db.execute('''
      CREATE TABLE IF NOT EXISTS chef_menu_assignments (
        id TEXT PRIMARY KEY,
        chef_id TEXT NOT NULL,
        menu_item_id TEXT NOT NULL,
        priority INTEGER DEFAULT 1,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        FOREIGN KEY (chef_id) REFERENCES chefs (id),
        FOREIGN KEY (menu_item_id) REFERENCES menu_items (id)
      )
    ''');

    // إدراج بيانات افتراضية للإعدادات المرنة
    await _insertConfigDefaults(db);
  }

  // إدراج بيانات افتراضية للإعدادات المرنة
  Future<void> _insertConfigDefaults(Database db) async {
    // التحقق من وجود إعدادات المطعم
    final configExists = await db.query('restaurant_config');
    if (configExists.isEmpty) {
      await db.insert('restaurant_config', {
        'id': 'default',
        'name': 'مطعم الذواقة',
        'address': 'الرياض، المملكة العربية السعودية',
        'phone': '+966501234567',
        'email': '<EMAIL>',
        'working_hours':
            '{"الأحد":"09:00-23:00","الاثنين":"09:00-23:00","الثلاثاء":"09:00-23:00","الأربعاء":"09:00-23:00","الخميس":"09:00-23:00","الجمعة":"14:00-23:00","السبت":"09:00-23:00"}',
        'service_types': '["dineIn","takeaway"]',
        'currency': 'ريال',
        'tax_rate': 0.15,
        'service_charge': 0.0,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
    }

    // إدراج قسم افتراضي للطاولات
    final sectionsExist = await db.query('table_sections');
    if (sectionsExist.isEmpty) {
      await db.insert('table_sections', {
        'id': 'main_hall',
        'name': 'القاعة الرئيسية',
        'description': 'القاعة الرئيسية للمطعم',
        'max_tables': 50,
        'is_active': 1,
        'created_at': DateTime.now().toIso8601String(),
      });
    }

    // إدراج طاولات افتراضية مرنة
    final tablesExist = await db.query('restaurant_tables');
    if (tablesExist.isEmpty) {
      for (int i = 1; i <= 10; i++) {
        await db.insert('restaurant_tables', {
          'id': 'table_$i',
          'number': i.toString(),
          'section': 'القاعة الرئيسية',
          'capacity': i <= 6 ? 4 : 6,
          'type': i <= 8 ? 'regular' : 'vip',
          'status': 'available',
          'created_at': DateTime.now().toIso8601String(),
        });
      }
    }

    // إدراج شيفات افتراضيين
    final chefsExist = await db.query('chefs');
    if (chefsExist.isEmpty) {
      final defaultChefs = [
        {
          'id': 'chef_pizza',
          'name': 'أحمد محمد',
          'username': 'pizza',
          'password': '123456',
          'specialty': 'شيف البيتزا',
          'description': 'متخصص في جميع أنواع البيتزا',
          'color': '#FF9800',
          'status': 'active',
          'working_days': 'الأحد,الاثنين,الثلاثاء,الأربعاء,الخميس',
          'start_time': '09:00',
          'end_time': '18:00',
          'assigned_categories': 'البيتزا',
          'assigned_menu_items': '',
          'receive_notifications': 1,
          'priority': 1,
          'created_at': DateTime.now().toIso8601String(),
        },
        {
          'id': 'chef_burger',
          'name': 'سارة أحمد',
          'username': 'burger',
          'password': '123456',
          'specialty': 'شيف البرجر',
          'description': 'متخصصة في البرجر والساندويتش',
          'color': '#8D6E63',
          'status': 'active',
          'working_days': 'الأحد,الاثنين,الثلاثاء,الأربعاء,الخميس',
          'start_time': '09:00',
          'end_time': '18:00',
          'assigned_categories': 'البرجر والساندويتش',
          'assigned_menu_items': '',
          'receive_notifications': 1,
          'priority': 2,
          'created_at': DateTime.now().toIso8601String(),
        },
        {
          'id': 'chef_general',
          'name': 'محمد علي',
          'username': 'chef',
          'password': '123456',
          'specialty': 'الشيف العام',
          'description': 'شيف عام لجميع الأصناف',
          'color': '#009688',
          'status': 'active',
          'working_days': 'الأحد,الاثنين,الثلاثاء,الأربعاء,الخميس,الجمعة,السبت',
          'start_time': '08:00',
          'end_time': '22:00',
          'assigned_categories': 'المشروبات,السلطات,الحلويات',
          'assigned_menu_items': '',
          'receive_notifications': 1,
          'priority': 3,
          'created_at': DateTime.now().toIso8601String(),
        },
      ];

      for (final chef in defaultChefs) {
        await db.insert('chefs', chef);
      }
    }
  }

  Future<void> _insertInitialData(Database db) async {
    // إضافة طاولات افتراضية
    for (int i = 1; i <= 10; i++) {
      await db.insert('tables', {
        'number': i,
        'capacity': i <= 4 ? 4 : (i <= 8 ? 6 : 8),
        'status': 0, // متاحة
        'location': i <= 5 ? 'الطابق الأول' : 'الطابق الثاني',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
    }

    // إضافة أصناف طعام افتراضية مع صور
    final defaultMenuItems = [
      {
        'name': 'شاورما لحم',
        'name_en': 'Beef Shawarma',
        'description': 'شاورما لحم طازجة مع الخضار والصلصة',
        'description_en': 'Fresh beef shawarma with vegetables and sauce',
        'price': 25.0,
        'category': 'أطباق رئيسية',
        'image_path':
            'https://images.unsplash.com/photo-1529006557810-274b9b2fc783?w=400&h=300&fit=crop',
      },
      {
        'name': 'شاورما دجاج',
        'name_en': 'Chicken Shawarma',
        'description': 'شاورما دجاج طازجة مع الخضار والصلصة',
        'description_en': 'Fresh chicken shawarma with vegetables and sauce',
        'price': 20.0,
        'category': 'أطباق رئيسية',
        'image_path':
            'https://images.unsplash.com/photo-1555939594-58d7cb561ad1?w=400&h=300&fit=crop',
      },
      {
        'name': 'فلافل',
        'name_en': 'Falafel',
        'description': 'فلافل طازج مع الطحينة والخضار',
        'description_en': 'Fresh falafel with tahini and vegetables',
        'price': 15.0,
        'category': 'أطباق رئيسية',
        'image_path':
            'https://images.unsplash.com/photo-1593504049359-74330189a345?w=400&h=300&fit=crop',
      },
      {
        'name': 'حمص',
        'name_en': 'Hummus',
        'description': 'حمص طازج مع زيت الزيتون',
        'description_en': 'Fresh hummus with olive oil',
        'price': 12.0,
        'category': 'مقبلات',
        'image_path':
            'https://images.unsplash.com/photo-1615937691194-97dbd4d3e4c4?w=400&h=300&fit=crop',
      },
      {
        'name': 'تبولة',
        'name_en': 'Tabbouleh',
        'description': 'سلطة تبولة طازجة',
        'description_en': 'Fresh tabbouleh salad',
        'price': 18.0,
        'category': 'سلطات',
        'image_path':
            'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop',
      },
      {
        'name': 'عصير برتقال',
        'name_en': 'Orange Juice',
        'description': 'عصير برتقال طازج',
        'description_en': 'Fresh orange juice',
        'price': 8.0,
        'category': 'مشروبات',
        'image_path':
            'https://images.unsplash.com/photo-1621506289937-a8e4df240d0b?w=400&h=300&fit=crop',
      },
      {
        'name': 'شاي',
        'name_en': 'Tea',
        'description': 'شاي أحمر',
        'description_en': 'Black tea',
        'price': 3.0,
        'category': 'مشروبات',
        'image_path':
            'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=300&fit=crop',
      },
      {
        'name': 'قهوة عربية',
        'name_en': 'Arabic Coffee',
        'description': 'قهوة عربية أصيلة',
        'description_en': 'Authentic Arabic coffee',
        'price': 5.0,
        'category': 'مشروبات',
        'image_path':
            'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=400&h=300&fit=crop',
      },
      {
        'name': 'برجر لحم',
        'name_en': 'Beef Burger',
        'description': 'برجر لحم مشوي مع البطاطس',
        'description_en': 'Grilled beef burger with fries',
        'price': 30.0,
        'category': 'أطباق رئيسية',
        'image_path':
            'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=400&h=300&fit=crop',
      },
      {
        'name': 'بيتزا مارجريتا',
        'name_en': 'Margherita Pizza',
        'description': 'بيتزا مارجريتا كلاسيكية',
        'description_en': 'Classic margherita pizza',
        'price': 35.0,
        'category': 'أطباق رئيسية',
        'image_path':
            'https://images.unsplash.com/photo-1513104890138-7c749659a591?w=400&h=300&fit=crop',
      },
      {
        'name': 'سلطة قيصر',
        'name_en': 'Caesar Salad',
        'description': 'سلطة قيصر مع الدجاج المشوي',
        'description_en': 'Caesar salad with grilled chicken',
        'price': 22.0,
        'category': 'سلطات',
        'image_path':
            'https://images.unsplash.com/photo-1546793665-c74683f339c1?w=400&h=300&fit=crop',
      },
      {
        'name': 'كنافة',
        'name_en': 'Kunafa',
        'description': 'كنافة بالجبن والقطر',
        'description_en': 'Kunafa with cheese and syrup',
        'price': 15.0,
        'category': 'حلويات',
        'image_path':
            'https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400&h=300&fit=crop',
      },
    ];

    for (final item in defaultMenuItems) {
      await db.insert('menu_items', {
        ...item,
        'is_available': 1,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
    }

    // إضافة إعدادات افتراضية
    final defaultSettings = [
      {'key': 'restaurant_name', 'value': 'مطعم الأصالة'},
      {'key': 'restaurant_name_en', 'value': 'Authenticity Restaurant'},
      {'key': 'tax_rate', 'value': '0.15'},
      {'key': 'currency', 'value': 'ريال'},
      {'key': 'currency_en', 'value': 'SAR'},
      {'key': 'language', 'value': 'ar'},
      {'key': 'print_receipt', 'value': 'true'},
      {'key': 'auto_backup', 'value': 'true'},
    ];

    for (final setting in defaultSettings) {
      await db.insert('settings', {
        ...setting,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
    }
  }

  // إغلاق قاعدة البيانات
  Future<void> close() async {
    final db = await database;
    await db.close();
  }

  // حذف قاعدة البيانات (للاختبار)
  Future<void> deleteDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, 'restaurant_management.db');
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
}
