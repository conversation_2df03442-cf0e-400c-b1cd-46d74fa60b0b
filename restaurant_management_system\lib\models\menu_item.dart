class MenuItem {
  final String id;
  final String name;
  final String nameEn;
  final String description;
  final String descriptionEn;
  final double price;
  final String category;
  final String? imagePath;
  String? get imageUrl => imagePath;
  final bool isAvailable;
  final DateTime createdAt;
  final DateTime updatedAt;

  MenuItem({
    required this.id,
    required this.name,
    required this.nameEn,
    required this.description,
    required this.descriptionEn,
    required this.price,
    required this.category,
    this.imagePath,
    this.isAvailable = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  // تحويل من Map إلى MenuItem
  factory MenuItem.fromMap(Map<String, dynamic> map) {
    return MenuItem(
      id: map['id'],
      name: map['name'] ?? '',
      nameEn: map['name_en'] ?? '',
      description: map['description'] ?? '',
      descriptionEn: map['description_en'] ?? '',
      price: (map['price'] ?? 0.0).toDouble(),
      category: map['category'] ?? '',
      imagePath: map['image_path'],
      isAvailable: (map['is_available'] ?? 1) == 1,
      createdAt:
          DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt:
          DateTime.parse(map['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  // تحويل من MenuItem إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'name_en': nameEn,
      'description': description,
      'description_en': descriptionEn,
      'price': price,
      'category': category,
      'image_path': imagePath,
      'is_available': isAvailable ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // إنشاء نسخة محدثة من MenuItem
  MenuItem copyWith({
    String? id,
    String? name,
    String? nameEn,
    String? description,
    String? descriptionEn,
    double? price,
    String? category,
    String? imagePath,
    bool? isAvailable,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MenuItem(
      id: id ?? this.id,
      name: name ?? this.name,
      nameEn: nameEn ?? this.nameEn,
      description: description ?? this.description,
      descriptionEn: descriptionEn ?? this.descriptionEn,
      price: price ?? this.price,
      category: category ?? this.category,
      imagePath: imagePath ?? this.imagePath,
      isAvailable: isAvailable ?? this.isAvailable,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'MenuItem{id: $id, name: $name, price: $price, category: $category}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MenuItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// فئات الطعام المحددة مسبقاً
class MenuCategories {
  static const String appetizers = 'مقبلات';
  static const String mainCourses = 'أطباق رئيسية';
  static const String desserts = 'حلويات';
  static const String beverages = 'مشروبات';
  static const String salads = 'سلطات';
  static const String soups = 'شوربات';
  static const String grills = 'مشاوي';
  static const String seafood = 'مأكولات بحرية';

  static const String appetizersEn = 'Appetizers';
  static const String mainCoursesEn = 'Main Courses';
  static const String dessertsEn = 'Desserts';
  static const String beveragesEn = 'Beverages';
  static const String saladsEn = 'Salads';
  static const String soupsEn = 'Soups';
  static const String grillsEn = 'Grills';
  static const String seafoodEn = 'Seafood';

  static List<String> get allCategories => [
        appetizers,
        mainCourses,
        desserts,
        beverages,
        salads,
        soups,
        grills,
        seafood,
      ];

  static List<String> get allCategoriesEn => [
        appetizersEn,
        mainCoursesEn,
        dessertsEn,
        beveragesEn,
        saladsEn,
        soupsEn,
        grillsEn,
        seafoodEn,
      ];

  static String getEnglishCategory(String arabicCategory) {
    switch (arabicCategory) {
      case appetizers:
        return appetizersEn;
      case mainCourses:
        return mainCoursesEn;
      case desserts:
        return dessertsEn;
      case beverages:
        return beveragesEn;
      case salads:
        return saladsEn;
      case soups:
        return soupsEn;
      case grills:
        return grillsEn;
      case seafood:
        return seafoodEn;
      default:
        return arabicCategory;
    }
  }

  static String getArabicCategory(String englishCategory) {
    switch (englishCategory) {
      case appetizersEn:
        return appetizers;
      case mainCoursesEn:
        return mainCourses;
      case dessertsEn:
        return desserts;
      case beveragesEn:
        return beverages;
      case saladsEn:
        return salads;
      case soupsEn:
        return soups;
      case grillsEn:
        return grills;
      case seafoodEn:
        return seafood;
      default:
        return englishCategory;
    }
  }
}
