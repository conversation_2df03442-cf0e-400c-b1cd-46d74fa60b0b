import 'package:flutter/material.dart';
import '../../models/chef.dart';
import '../../models/order.dart';
import '../../models/menu_item.dart';
import '../../services/order_service.dart';
import '../../services/restaurant_config_service.dart';
import '../../services/menu_service.dart';
import '../../services/audio_service.dart';
import '../../widgets/kitchen/kitchen_order_card.dart';

class ChefScreen extends StatefulWidget {
  final Chef chef;

  const ChefScreen({
    super.key,
    required this.chef,
  });

  @override
  State<ChefScreen> createState() => _ChefScreenState();
}

class _ChefScreenState extends State<ChefScreen> {
  final OrderService _orderService = OrderService();
  final RestaurantConfigService _configService = RestaurantConfigService();
  final MenuService _menuService = MenuService();
  final AudioService _audioService = AudioService();

  List<Order> _orders = [];
  List<MenuItem> _chefMenuItems = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
    _startOrderPolling();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      await _loadChefMenuItems();
      await _loadOrders();
    } catch (e) {
      _showError('خطأ في تحميل البيانات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadChefMenuItems() async {
    try {
      final allMenuItems = await _menuService.getAllMenuItems();
      
      // تصفية الأصناف حسب المجموعات المخصصة للشيف
      final chefItems = allMenuItems.where((item) {
        // التحقق من المجموعات المخصصة
        if (widget.chef.assignedCategories.contains(item.category)) {
          return true;
        }
        // التحقق من الأصناف المحددة
        if (widget.chef.assignedMenuItems.contains(item.id)) {
          return true;
        }
        return false;
      }).toList();

      setState(() {
        _chefMenuItems = chefItems;
      });
    } catch (e) {
      print('خطأ في تحميل أصناف الشيف: $e');
    }
  }

  Future<void> _loadOrders() async {
    try {
      final allOrders = await _orderService.getActiveOrders();
      
      // تصفية الطلبات التي تحتوي على أصناف مخصصة لهذا الشيف
      final chefOrders = <Order>[];
      
      for (final order in allOrders) {
        bool hasChefItems = false;
        
        for (final orderItem in order.items) {
          // التحقق من أن الصنف مخصص لهذا الشيف
          final menuItem = _chefMenuItems.firstWhere(
            (item) => item.id == orderItem.menuItemId,
            orElse: () => MenuItem(
              id: '',
              name: '',
              nameEn: '',
              description: '',
              descriptionEn: '',
              price: 0,
              category: '',
              isAvailable: false,
              createdAt: DateTime.now(),
            ),
          );
          
          if (menuItem.id.isNotEmpty) {
            hasChefItems = true;
            break;
          }
        }
        
        if (hasChefItems) {
          chefOrders.add(order);
        }
      }

      setState(() {
        _orders = chefOrders;
      });
    } catch (e) {
      print('خطأ في تحميل الطلبات: $e');
    }
  }

  void _startOrderPolling() {
    // تحديث الطلبات كل 30 ثانية
    Future.delayed(const Duration(seconds: 30), () {
      if (mounted) {
        _loadOrders();
        _startOrderPolling();
      }
    });
  }

  Future<void> _updateOrderStatus(String orderId, OrderStatus newStatus) async {
    try {
      await _orderService.updateOrderStatus(orderId, newStatus);
      await _loadOrders();
      
      if (newStatus == OrderStatus.ready) {
        _audioService.playNewOrderSound();
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تحديث حالة الطلب إلى ${newStatus.displayName}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      _showError('خطأ في تحديث حالة الطلب: $e');
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'مطبخ ${widget.chef.name}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Color(int.parse(widget.chef.color.substring(1), radix: 16) + 0xFF000000),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _loadData,
            icon: const Icon(Icons.refresh),
          ),
          IconButton(
            onPressed: () {
              _showChefInfo();
            },
            icon: const Icon(Icons.info),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // معلومات الشيف
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Color(int.parse(widget.chef.color.substring(1), radix: 16) + 0xFF000000).withOpacity(0.1),
                    border: Border(
                      bottom: BorderSide(
                        color: Color(int.parse(widget.chef.color.substring(1), radix: 16) + 0xFF000000),
                        width: 2,
                      ),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.chef.specialty,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'الطلبات النشطة: ${_orders.length}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                      if (widget.chef.assignedCategories.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Text(
                          'المجموعات: ${widget.chef.assignedCategories.join(', ')}',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                
                // قائمة الطلبات
                Expanded(
                  child: _orders.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.restaurant_menu,
                                size: 64,
                                color: Colors.grey[400],
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'لا توجد طلبات جديدة',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.grey[600],
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'ستظهر الطلبات الجديدة هنا تلقائياً',
                                style: TextStyle(
                                  color: Colors.grey[500],
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: _orders.length,
                          itemBuilder: (context, index) {
                            final order = _orders[index];
                            return KitchenOrderCard(
                              order: order,
                              chefMenuItems: _chefMenuItems,
                              chefColor: widget.chef.color,
                              onStatusUpdate: _updateOrderStatus,
                            );
                          },
                        ),
                ),
              ],
            ),
    );
  }

  void _showChefInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('معلومات ${widget.chef.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('التخصص:', widget.chef.specialty),
            _buildInfoRow('الحالة:', widget.chef.status.displayName),
            _buildInfoRow('أوقات العمل:', '${widget.chef.startTime} - ${widget.chef.endTime}'),
            _buildInfoRow('أيام العمل:', widget.chef.workingDays.join(', ')),
            if (widget.chef.assignedCategories.isNotEmpty)
              _buildInfoRow('المجموعات المخصصة:', widget.chef.assignedCategories.join(', ')),
            if (widget.chef.assignedMenuItems.isNotEmpty)
              _buildInfoRow('عدد الأصناف المخصصة:', '${widget.chef.assignedMenuItems.length} صنف'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
