import 'package:flutter/material.dart';
import '../models/menu_item.dart';
import '../services/menu_service.dart';
import '../widgets/menu_item_card.dart';
import '../widgets/add_menu_item_dialog.dart';

class MenuManagementScreen extends StatefulWidget {
  const MenuManagementScreen({super.key});

  @override
  State<MenuManagementScreen> createState() => _MenuManagementScreenState();
}

class _MenuManagementScreenState extends State<MenuManagementScreen> {
  final MenuService _menuService = MenuService();
  List<MenuItem> _menuItems = [];
  List<String> _categories = [];
  String _selectedCategory = 'الكل';
  bool _isLoading = true;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadMenuItems();
    _loadCategories();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadMenuItems() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final items = await _menuService.getAllMenuItems();
      setState(() {
        _menuItems = items;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل القائمة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadCategories() async {
    try {
      final categories = await _menuService.getAvailableCategories();
      setState(() {
        _categories = ['الكل', ...categories];
      });
    } catch (e) {
      print('خطأ في تحميل الفئات: $e');
    }
  }

  List<MenuItem> get _filteredItems {
    List<MenuItem> filtered = _menuItems;

    // تصفية حسب الفئة
    if (_selectedCategory != 'الكل') {
      filtered = filtered.where((item) => item.category == _selectedCategory).toList();
    }

    // تصفية حسب البحث
    final searchQuery = _searchController.text.toLowerCase();
    if (searchQuery.isNotEmpty) {
      filtered = filtered.where((item) =>
          item.name.toLowerCase().contains(searchQuery) ||
          item.nameEn.toLowerCase().contains(searchQuery) ||
          item.description.toLowerCase().contains(searchQuery)).toList();
    }

    return filtered;
  }

  Future<void> _addMenuItem() async {
    final result = await showDialog<MenuItem>(
      context: context,
      builder: (context) => const AddMenuItemDialog(),
    );

    if (result != null) {
      try {
        await _menuService.addMenuItem(result);
        await _loadMenuItems();
        await _loadCategories();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إضافة الصنف بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في إضافة الصنف: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _editMenuItem(MenuItem item) async {
    final result = await showDialog<MenuItem>(
      context: context,
      builder: (context) => AddMenuItemDialog(menuItem: item),
    );

    if (result != null) {
      try {
        await _menuService.updateMenuItem(result);
        await _loadMenuItems();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث الصنف بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تحديث الصنف: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _deleteMenuItem(MenuItem item) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف "${item.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true && item.id != null) {
      try {
        await _menuService.deleteMenuItem(item.id!);
        await _loadMenuItems();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف الصنف بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف الصنف: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _toggleAvailability(MenuItem item) async {
    if (item.id != null) {
      try {
        await _menuService.toggleItemAvailability(item.id!);
        await _loadMenuItems();
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تغيير حالة التوفر: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة القائمة'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addMenuItem,
            tooltip: 'إضافة صنف جديد',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والتصفية
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // حقل البحث
                TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    hintText: 'البحث في القائمة...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) => setState(() {}),
                ),
                const SizedBox(height: 16),
                
                // فلتر الفئات
                SizedBox(
                  height: 40,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _categories.length,
                    itemBuilder: (context, index) {
                      final category = _categories[index];
                      final isSelected = category == _selectedCategory;
                      
                      return Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: FilterChip(
                          label: Text(category),
                          selected: isSelected,
                          onSelected: (selected) {
                            setState(() {
                              _selectedCategory = category;
                            });
                          },
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          
          // قائمة الأصناف
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredItems.isEmpty
                    ? const Center(
                        child: Text(
                          'لا توجد أصناف',
                          style: TextStyle(fontSize: 18),
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _filteredItems.length,
                        itemBuilder: (context, index) {
                          final item = _filteredItems[index];
                          return MenuItemCard(
                            menuItem: item,
                            onEdit: () => _editMenuItem(item),
                            onDelete: () => _deleteMenuItem(item),
                            onToggleAvailability: () => _toggleAvailability(item),
                          );
                        },
                      ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addMenuItem,
        child: const Icon(Icons.add),
        tooltip: 'إضافة صنف جديد',
      ),
    );
  }
}
