// نموذج إعدادات المطعم
class RestaurantConfig {
  final String id;
  final String name;
  final String? logo;
  final String address;
  final String phone;
  final String email;
  final Map<String, String> workingHours; // {"monday": "9:00-22:00", ...}
  final List<ServiceType> serviceTypes;
  final String currency;
  final double taxRate;
  final double serviceCharge;
  final DateTime createdAt;
  final DateTime updatedAt;

  RestaurantConfig({
    required this.id,
    required this.name,
    this.logo,
    required this.address,
    required this.phone,
    required this.email,
    required this.workingHours,
    required this.serviceTypes,
    this.currency = 'ريال',
    this.taxRate = 0.15,
    this.serviceCharge = 0.0,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'logo': logo,
      'address': address,
      'phone': phone,
      'email': email,
      'working_hours': _encodeMap(workingHours),
      'service_types': _encodeList(serviceTypes.map((e) => e.name).toList()),
      'currency': currency,
      'tax_rate': taxRate,
      'service_charge': serviceCharge,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String _encodeMap(Map<String, String> map) {
    return map.entries.map((e) => '${e.key}:${e.value}').join(',');
  }

  String _encodeList(List<String> list) {
    return list.join(',');
  }

  factory RestaurantConfig.fromMap(Map<String, dynamic> map) {
    return RestaurantConfig(
      id: map['id'],
      name: map['name'],
      logo: map['logo'],
      address: map['address'],
      phone: map['phone'],
      email: map['email'],
      workingHours: _decodeMap(map['working_hours'] ?? ''),
      serviceTypes: _decodeServiceTypes(map['service_types'] ?? ''),
      currency: map['currency'] ?? 'ريال',
      taxRate: map['tax_rate']?.toDouble() ?? 0.15,
      serviceCharge: map['service_charge']?.toDouble() ?? 0.0,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  static Map<String, String> _decodeMap(String encoded) {
    if (encoded.isEmpty) return {};
    final Map<String, String> result = {};
    for (final pair in encoded.split(',')) {
      final parts = pair.split(':');
      if (parts.length == 2) {
        result[parts[0]] = parts[1];
      }
    }
    return result;
  }

  static List<ServiceType> _decodeServiceTypes(String encoded) {
    if (encoded.isEmpty) return [ServiceType.dineIn];
    return encoded.split(',').map((name) {
      return ServiceType.values.firstWhere(
        (type) => type.name == name,
        orElse: () => ServiceType.dineIn,
      );
    }).toList();
  }

  RestaurantConfig copyWith({
    String? id,
    String? name,
    String? logo,
    String? address,
    String? phone,
    String? email,
    Map<String, String>? workingHours,
    List<ServiceType>? serviceTypes,
    String? currency,
    double? taxRate,
    double? serviceCharge,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RestaurantConfig(
      id: id ?? this.id,
      name: name ?? this.name,
      logo: logo ?? this.logo,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      workingHours: workingHours ?? this.workingHours,
      serviceTypes: serviceTypes ?? this.serviceTypes,
      currency: currency ?? this.currency,
      taxRate: taxRate ?? this.taxRate,
      serviceCharge: serviceCharge ?? this.serviceCharge,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

enum ServiceType {
  dineIn('تناول في المطعم', 'Dine In'),
  takeaway('تيك أواي', 'Takeaway'),
  delivery('توصيل', 'Delivery');

  const ServiceType(this.nameAr, this.nameEn);
  final String nameAr;
  final String nameEn;

  String get displayName => nameAr;
}
