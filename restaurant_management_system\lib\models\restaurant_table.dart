// نموذج الطاولات المرن
class RestaurantTable {
  final String id;
  final String number; // رقم أو اسم الطاولة (مرن)
  final String section; // القسم أو القاعة
  final int capacity; // عدد الأشخاص
  final TableType type;
  final TableStatus status;
  final double? positionX; // موقع على الخريطة
  final double? positionY;
  final String? notes;
  final DateTime createdAt;
  final DateTime? lastUpdated;

  RestaurantTable({
    required this.id,
    required this.number,
    this.section = 'القاعة الرئيسية',
    required this.capacity,
    this.type = TableType.regular,
    this.status = TableStatus.available,
    this.positionX,
    this.positionY,
    this.notes,
    required this.createdAt,
    this.lastUpdated,
  });

  // اسم الطاولة الكامل
  String get fullName => section.isNotEmpty ? '$section - $number' : number;

  // لون الطاولة حسب الحالة
  String get statusColor {
    switch (status) {
      case TableStatus.available:
        return '#4CAF50'; // أخضر
      case TableStatus.occupied:
        return '#FF5722'; // أحمر
      case TableStatus.reserved:
        return '#FF9800'; // برتقالي
      case TableStatus.cleaning:
        return '#2196F3'; // أزرق
      case TableStatus.outOfService:
        return '#9E9E9E'; // رمادي
    }
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'number': number,
      'section': section,
      'capacity': capacity,
      'type': type.name,
      'status': status.name,
      'position_x': positionX,
      'position_y': positionY,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'last_updated': lastUpdated?.toIso8601String(),
    };
  }

  factory RestaurantTable.fromMap(Map<String, dynamic> map) {
    return RestaurantTable(
      id: map['id'],
      number: map['number'],
      section: map['section'] ?? 'القاعة الرئيسية',
      capacity: map['capacity'],
      type: TableType.values.firstWhere(
        (t) => t.name == map['type'],
        orElse: () => TableType.regular,
      ),
      status: TableStatus.values.firstWhere(
        (s) => s.name == map['status'],
        orElse: () => TableStatus.available,
      ),
      positionX: map['position_x']?.toDouble(),
      positionY: map['position_y']?.toDouble(),
      notes: map['notes'],
      createdAt: DateTime.parse(map['created_at']),
      lastUpdated: map['last_updated'] != null 
          ? DateTime.parse(map['last_updated']) 
          : null,
    );
  }

  RestaurantTable copyWith({
    String? id,
    String? number,
    String? section,
    int? capacity,
    TableType? type,
    TableStatus? status,
    double? positionX,
    double? positionY,
    String? notes,
    DateTime? createdAt,
    DateTime? lastUpdated,
  }) {
    return RestaurantTable(
      id: id ?? this.id,
      number: number ?? this.number,
      section: section ?? this.section,
      capacity: capacity ?? this.capacity,
      type: type ?? this.type,
      status: status ?? this.status,
      positionX: positionX ?? this.positionX,
      positionY: positionY ?? this.positionY,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

enum TableType {
  regular('عادية', 'Regular'),
  vip('كبار الشخصيات', 'VIP'),
  outdoor('خارجية', 'Outdoor'),
  private('خاصة', 'Private'),
  bar('بار', 'Bar');

  const TableType(this.nameAr, this.nameEn);
  final String nameAr;
  final String nameEn;

  String get displayName => nameAr;
}

enum TableStatus {
  available('متاحة', 'Available'),
  occupied('مشغولة', 'Occupied'),
  reserved('محجوزة', 'Reserved'),
  cleaning('قيد التنظيف', 'Cleaning'),
  outOfService('خارج الخدمة', 'Out of Service');

  const TableStatus(this.nameAr, this.nameEn);
  final String nameAr;
  final String nameEn;

  String get displayName => nameAr;
}

// نموذج قسم أو قاعة
class TableSection {
  final String id;
  final String name;
  final String? description;
  final int maxTables;
  final bool isActive;
  final DateTime createdAt;

  TableSection({
    required this.id,
    required this.name,
    this.description,
    this.maxTables = 50,
    this.isActive = true,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'max_tables': maxTables,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory TableSection.fromMap(Map<String, dynamic> map) {
    return TableSection(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      maxTables: map['max_tables'] ?? 50,
      isActive: map['is_active'] == 1,
      createdAt: DateTime.parse(map['created_at']),
    );
  }
}
