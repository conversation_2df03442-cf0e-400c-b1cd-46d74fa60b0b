import 'package:flutter/material.dart';
import '../models/order.dart';

class OrderCard extends StatelessWidget {
  final Order order;
  final Function(OrderStatus) onStatusChanged;

  const OrderCard({
    super.key,
    required this.order,
    required this.onStatusChanged,
  });

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Colors.orange;
      case OrderStatus.preparing:
        return Colors.blue;
      case OrderStatus.ready:
        return Colors.green;
      case OrderStatus.completed:
        return Colors.grey;
      case OrderStatus.cancelled:
        return Colors.red;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس الطلب
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'طلب رقم ${order.orderNumber}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                        textDirection: TextDirection.rtl,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        order.type.displayName,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                        textDirection: TextDirection.rtl,
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(order.status).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: _getStatusColor(order.status),
                    ),
                  ),
                  child: Text(
                    order.status.displayName,
                    style: TextStyle(
                      color: _getStatusColor(order.status),
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // معلومات الطلب
            if (order.tableNumber != null) ...[
              Row(
                children: [
                  const Icon(Icons.table_restaurant, size: 16),
                  const SizedBox(width: 8),
                  Text('طاولة ${order.tableNumber}'),
                ],
              ),
              const SizedBox(height: 8),
            ],
            
            if (order.customerName != null) ...[
              Row(
                children: [
                  const Icon(Icons.person, size: 16),
                  const SizedBox(width: 8),
                  Text(order.customerName!),
                ],
              ),
              const SizedBox(height: 8),
            ],
            
            if (order.customerPhone != null) ...[
              Row(
                children: [
                  const Icon(Icons.phone, size: 16),
                  const SizedBox(width: 8),
                  Text(order.customerPhone!),
                ],
              ),
              const SizedBox(height: 8),
            ],
            
            // أصناف الطلب
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'أصناف الطلب:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...order.items.map((item) => Padding(
                        padding: const EdgeInsets.only(bottom: 4),
                        child: Row(
                          children: [
                            Text('${item.quantity}x'),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                item.menuItem?.name ?? 'صنف غير معروف',
                                textDirection: TextDirection.rtl,
                              ),
                            ),
                            Text(
                              '${item.totalPrice.toStringAsFixed(2)} ريال',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      )),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // المجموع والوقت
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الوقت: ${_formatDateTime(order.createdAt)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      if (order.notes != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          'ملاحظات: ${order.notes}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                          textDirection: TextDirection.rtl,
                        ),
                      ],
                    ],
                  ),
                ),
                Text(
                  '${order.totalAmount.toStringAsFixed(2)} ريال',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // أزرار تغيير الحالة
            if (order.status != OrderStatus.completed &&
                order.status != OrderStatus.cancelled) ...[
              Wrap(
                spacing: 8,
                children: _buildStatusButtons(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  List<Widget> _buildStatusButtons() {
    List<Widget> buttons = [];
    
    switch (order.status) {
      case OrderStatus.pending:
        buttons.addAll([
          ElevatedButton.icon(
            onPressed: () => onStatusChanged(OrderStatus.preparing),
            icon: const Icon(Icons.restaurant, size: 16),
            label: const Text('بدء التحضير'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
          OutlinedButton.icon(
            onPressed: () => onStatusChanged(OrderStatus.cancelled),
            icon: const Icon(Icons.cancel, size: 16),
            label: const Text('إلغاء'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.red,
            ),
          ),
        ]);
        break;
        
      case OrderStatus.preparing:
        buttons.add(
          ElevatedButton.icon(
            onPressed: () => onStatusChanged(OrderStatus.ready),
            icon: const Icon(Icons.check_circle, size: 16),
            label: const Text('جاهز'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        );
        break;
        
      case OrderStatus.ready:
        buttons.add(
          ElevatedButton.icon(
            onPressed: () => onStatusChanged(OrderStatus.completed),
            icon: const Icon(Icons.done_all, size: 16),
            label: const Text('مكتمل'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey,
              foregroundColor: Colors.white,
            ),
          ),
        );
        break;
        
      default:
        break;
    }
    
    return buttons;
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')} - '
           '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }
}
