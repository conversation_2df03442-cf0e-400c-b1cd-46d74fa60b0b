# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\PROJECTS\\restaurant_management_system\\restaurant_management_system" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\flutter"
  "PROJECT_DIR=D:\\PROJECTS\\restaurant_management_system\\restaurant_management_system"
  "FLUTTER_ROOT=C:\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\PROJECTS\\restaurant_management_system\\restaurant_management_system\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\PROJECTS\\restaurant_management_system\\restaurant_management_system"
  "FLUTTER_TARGET=D:\\PROJECTS\\restaurant_management_system\\restaurant_management_system\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\PROJECTS\\restaurant_management_system\\restaurant_management_system\\.dart_tool\\package_config.json"
)
