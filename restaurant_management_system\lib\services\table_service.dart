import 'package:sqflite/sqflite.dart';
import '../models/table.dart';
import 'database_service.dart';

class TableService {
  static final TableService _instance = TableService._internal();
  factory TableService() => _instance;
  TableService._internal();

  final DatabaseService _databaseService = DatabaseService();

  // إضافة طاولة جديدة
  Future<int> addTable(RestaurantTable table) async {
    final db = await _databaseService.database;
    final id = await db.insert('tables', table.toMap());
    return id;
  }

  // تحديث طاولة موجودة
  Future<int> updateTable(RestaurantTable table) async {
    final db = await _databaseService.database;
    return await db.update(
      'tables',
      table.copyWith(updatedAt: DateTime.now()).toMap(),
      where: 'id = ?',
      whereArgs: [table.id],
    );
  }

  // حذف طاولة
  Future<int> deleteTable(int id) async {
    final db = await _databaseService.database;
    return await db.delete(
      'tables',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // الحصول على طاولة بالمعرف
  Future<RestaurantTable?> getTableById(int id) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tables',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return RestaurantTable.fromMap(maps.first);
    }
    return null;
  }

  // الحصول على طاولة برقمها
  Future<RestaurantTable?> getTableByNumber(int number) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tables',
      where: 'number = ?',
      whereArgs: [number],
    );

    if (maps.isNotEmpty) {
      return RestaurantTable.fromMap(maps.first);
    }
    return null;
  }

  // الحصول على جميع الطاولات
  Future<List<RestaurantTable>> getAllTables() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tables',
      orderBy: 'number',
    );

    return List.generate(maps.length, (i) {
      return RestaurantTable.fromMap(maps[i]);
    });
  }

  // الحصول على الطاولات حسب الحالة
  Future<List<RestaurantTable>> getTablesByStatus(TableStatus status) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tables',
      where: 'status = ?',
      whereArgs: [status.index],
      orderBy: 'number',
    );

    return List.generate(maps.length, (i) {
      return RestaurantTable.fromMap(maps[i]);
    });
  }

  // الحصول على الطاولات المتاحة
  Future<List<RestaurantTable>> getAvailableTables() async {
    return await getTablesByStatus(TableStatus.available);
  }

  // الحصول على الطاولات المشغولة
  Future<List<RestaurantTable>> getOccupiedTables() async {
    return await getTablesByStatus(TableStatus.occupied);
  }

  // تحديث حالة الطاولة
  Future<int> updateTableStatus(int tableId, TableStatus status) async {
    final db = await _databaseService.database;
    return await db.update(
      'tables',
      {
        'status': status.index,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [tableId],
    );
  }

  // تحديث حالة الطاولة برقمها
  Future<int> updateTableStatusByNumber(int tableNumber, TableStatus status) async {
    final db = await _databaseService.database;
    return await db.update(
      'tables',
      {
        'status': status.index,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'number = ?',
      whereArgs: [tableNumber],
    );
  }

  // حجز طاولة
  Future<int> reserveTable(int tableId, String reservedBy, DateTime reservationTime) async {
    final db = await _databaseService.database;
    return await db.update(
      'tables',
      {
        'status': TableStatus.reserved.index,
        'reserved_by': reservedBy,
        'reservation_time': reservationTime.toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [tableId],
    );
  }

  // إلغاء حجز طاولة
  Future<int> cancelReservation(int tableId) async {
    final db = await _databaseService.database;
    return await db.update(
      'tables',
      {
        'status': TableStatus.available.index,
        'reserved_by': null,
        'reservation_time': null,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [tableId],
    );
  }

  // إحصائيات الطاولات
  Future<Map<String, int>> getTablesStatistics() async {
    final db = await _databaseService.database;
    
    final totalResult = await db.rawQuery('SELECT COUNT(*) as count FROM tables');
    final availableResult = await db.rawQuery('SELECT COUNT(*) as count FROM tables WHERE status = 0');
    final occupiedResult = await db.rawQuery('SELECT COUNT(*) as count FROM tables WHERE status = 1');
    final reservedResult = await db.rawQuery('SELECT COUNT(*) as count FROM tables WHERE status = 2');
    final outOfServiceResult = await db.rawQuery('SELECT COUNT(*) as count FROM tables WHERE status = 3');

    return {
      'total': Sqflite.firstIntValue(totalResult) ?? 0,
      'available': Sqflite.firstIntValue(availableResult) ?? 0,
      'occupied': Sqflite.firstIntValue(occupiedResult) ?? 0,
      'reserved': Sqflite.firstIntValue(reservedResult) ?? 0,
      'outOfService': Sqflite.firstIntValue(outOfServiceResult) ?? 0,
    };
  }

  // البحث في الطاولات
  Future<List<RestaurantTable>> searchTables(String query) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tables',
      where: 'CAST(number AS TEXT) LIKE ? OR location LIKE ? OR notes LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'number',
    );

    return List.generate(maps.length, (i) {
      return RestaurantTable.fromMap(maps[i]);
    });
  }

  // الحصول على عدد الطاولات
  Future<int> getTablesCount() async {
    final db = await _databaseService.database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM tables');
    return Sqflite.firstIntValue(result) ?? 0;
  }

  // التحقق من توفر رقم طاولة
  Future<bool> isTableNumberAvailable(int number, {int? excludeId}) async {
    final db = await _databaseService.database;
    String whereClause = 'number = ?';
    List<dynamic> whereArgs = [number];
    
    if (excludeId != null) {
      whereClause += ' AND id != ?';
      whereArgs.add(excludeId);
    }
    
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM tables WHERE $whereClause',
      whereArgs,
    );
    
    return (Sqflite.firstIntValue(result) ?? 0) == 0;
  }

  // حذف جميع الطاولات (للاختبار)
  Future<int> deleteAllTables() async {
    final db = await _databaseService.database;
    return await db.delete('tables');
  }
}
