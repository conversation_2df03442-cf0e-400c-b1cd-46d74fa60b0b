import 'package:sqflite/sqflite.dart';
import '../models/restaurant_config.dart';
import '../models/restaurant_table.dart';
import '../models/chef.dart';
import '../models/menu_item.dart';
import 'database_service.dart';

class RestaurantConfigService {
  static final RestaurantConfigService _instance = RestaurantConfigService._internal();
  factory RestaurantConfigService() => _instance;
  RestaurantConfigService._internal();

  final DatabaseService _databaseService = DatabaseService();

  // إنشاء جداول الإعدادات
  Future<void> createConfigTables() async {
    final db = await _databaseService.database;

    // جدول إعدادات المطعم
    await db.execute('''
      CREATE TABLE IF NOT EXISTS restaurant_config (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        logo TEXT,
        address TEXT NOT NULL,
        phone TEXT NOT NULL,
        email TEXT NOT NULL,
        working_hours TEXT NOT NULL,
        service_types TEXT NOT NULL,
        currency TEXT DEFAULT 'ريال',
        tax_rate REAL DEFAULT 0.15,
        service_charge REAL DEFAULT 0.0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // جدول أقسام الطاولات
    await db.execute('''
      CREATE TABLE IF NOT EXISTS table_sections (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        max_tables INTEGER DEFAULT 50,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL
      )
    ''');

    // جدول الطاولات
    await db.execute('''
      CREATE TABLE IF NOT EXISTS restaurant_tables (
        id TEXT PRIMARY KEY,
        number TEXT NOT NULL,
        section TEXT DEFAULT 'القاعة الرئيسية',
        capacity INTEGER NOT NULL,
        type TEXT DEFAULT 'regular',
        status TEXT DEFAULT 'available',
        position_x REAL,
        position_y REAL,
        notes TEXT,
        created_at TEXT NOT NULL,
        last_updated TEXT
      )
    ''');

    // جدول الشيفات
    await db.execute('''
      CREATE TABLE IF NOT EXISTS chefs (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        username TEXT UNIQUE NOT NULL,
        specialty TEXT NOT NULL,
        description TEXT,
        color TEXT NOT NULL,
        status TEXT DEFAULT 'active',
        working_days TEXT NOT NULL,
        start_time TEXT NOT NULL,
        end_time TEXT NOT NULL,
        device_id TEXT,
        receive_notifications INTEGER DEFAULT 1,
        priority INTEGER DEFAULT 5,
        created_at TEXT NOT NULL,
        last_login TEXT
      )
    ''');

    // جدول ربط الشيفات بالأصناف
    await db.execute('''
      CREATE TABLE IF NOT EXISTS chef_menu_assignments (
        id TEXT PRIMARY KEY,
        chef_id TEXT NOT NULL,
        menu_item_id TEXT NOT NULL,
        priority INTEGER DEFAULT 1,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        FOREIGN KEY (chef_id) REFERENCES chefs (id),
        FOREIGN KEY (menu_item_id) REFERENCES menu_items (id)
      )
    ''');

    // إدراج بيانات افتراضية إذا لم تكن موجودة
    await _insertDefaultData();
  }

  // إدراج بيانات افتراضية
  Future<void> _insertDefaultData() async {
    final db = await _databaseService.database;

    // التحقق من وجود إعدادات المطعم
    final configExists = await db.query('restaurant_config');
    if (configExists.isEmpty) {
      final defaultConfig = RestaurantConfig(
        id: 'default',
        name: 'مطعم الذواقة',
        address: 'الرياض، المملكة العربية السعودية',
        phone: '+966501234567',
        email: '<EMAIL>',
        workingHours: {
          'الأحد': '09:00-23:00',
          'الاثنين': '09:00-23:00',
          'الثلاثاء': '09:00-23:00',
          'الأربعاء': '09:00-23:00',
          'الخميس': '09:00-23:00',
          'الجمعة': '14:00-23:00',
          'السبت': '09:00-23:00',
        },
        serviceTypes: [ServiceType.dineIn, ServiceType.takeaway],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      await db.insert('restaurant_config', defaultConfig.toMap());
    }

    // إدراج قسم افتراضي للطاولات
    final sectionsExist = await db.query('table_sections');
    if (sectionsExist.isEmpty) {
      final defaultSection = TableSection(
        id: 'main_hall',
        name: 'القاعة الرئيسية',
        description: 'القاعة الرئيسية للمطعم',
        createdAt: DateTime.now(),
      );
      await db.insert('table_sections', defaultSection.toMap());
    }

    // إدراج طاولات افتراضية
    final tablesExist = await db.query('restaurant_tables');
    if (tablesExist.isEmpty) {
      for (int i = 1; i <= 10; i++) {
        final table = RestaurantTable(
          id: 'table_$i',
          number: i.toString(),
          section: 'القاعة الرئيسية',
          capacity: i <= 6 ? 4 : 6,
          type: i <= 8 ? TableType.regular : TableType.vip,
          createdAt: DateTime.now(),
        );
        await db.insert('restaurant_tables', table.toMap());
      }
    }

    // إدراج شيفات افتراضيين
    final chefsExist = await db.query('chefs');
    if (chefsExist.isEmpty) {
      final defaultChefs = [
        Chef(
          id: 'chef_pizza',
          name: 'أحمد محمد',
          username: 'pizza',
          specialty: 'شيف البيتزا',
          description: 'متخصص في جميع أنواع البيتزا',
          color: '#FF9800',
          workingDays: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس'],
          startTime: '09:00',
          endTime: '18:00',
          priority: 1,
          createdAt: DateTime.now(),
        ),
        Chef(
          id: 'chef_burger',
          name: 'سارة أحمد',
          username: 'burger',
          specialty: 'شيف البرجر',
          description: 'متخصصة في البرجر والساندويتش',
          color: '#8D6E63',
          workingDays: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس'],
          startTime: '09:00',
          endTime: '18:00',
          priority: 2,
          createdAt: DateTime.now(),
        ),
        Chef(
          id: 'chef_general',
          name: 'محمد علي',
          username: 'chef',
          specialty: 'الشيف العام',
          description: 'شيف عام لجميع الأصناف',
          color: '#009688',
          workingDays: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
          startTime: '08:00',
          endTime: '22:00',
          priority: 3,
          createdAt: DateTime.now(),
        ),
      ];

      for (final chef in defaultChefs) {
        await db.insert('chefs', chef.toMap());
      }
    }
  }

  // الحصول على إعدادات المطعم
  Future<RestaurantConfig?> getRestaurantConfig() async {
    final db = await _databaseService.database;
    final maps = await db.query('restaurant_config', limit: 1);
    
    if (maps.isNotEmpty) {
      return RestaurantConfig.fromMap(maps.first);
    }
    return null;
  }

  // تحديث إعدادات المطعم
  Future<void> updateRestaurantConfig(RestaurantConfig config) async {
    final db = await _databaseService.database;
    final updatedConfig = config.copyWith(updatedAt: DateTime.now());
    
    await db.update(
      'restaurant_config',
      updatedConfig.toMap(),
      where: 'id = ?',
      whereArgs: [config.id],
    );
  }

  // الحصول على جميع الطاولات
  Future<List<RestaurantTable>> getAllTables() async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'restaurant_tables',
      orderBy: 'section ASC, CAST(number AS INTEGER) ASC',
    );
    
    return maps.map((map) => RestaurantTable.fromMap(map)).toList();
  }

  // إضافة طاولة جديدة
  Future<void> addTable(RestaurantTable table) async {
    final db = await _databaseService.database;
    await db.insert('restaurant_tables', table.toMap());
  }

  // تحديث طاولة
  Future<void> updateTable(RestaurantTable table) async {
    final db = await _databaseService.database;
    final updatedTable = table.copyWith(lastUpdated: DateTime.now());
    
    await db.update(
      'restaurant_tables',
      updatedTable.toMap(),
      where: 'id = ?',
      whereArgs: [table.id],
    );
  }

  // حذف طاولة
  Future<void> deleteTable(String tableId) async {
    final db = await _databaseService.database;
    await db.delete(
      'restaurant_tables',
      where: 'id = ?',
      whereArgs: [tableId],
    );
  }

  // الحصول على جميع الشيفات
  Future<List<Chef>> getAllChefs() async {
    final db = await _databaseService.database;
    final maps = await db.query(
      'chefs',
      orderBy: 'priority ASC, name ASC',
    );
    
    return maps.map((map) => Chef.fromMap(map)).toList();
  }

  // إضافة شيف جديد
  Future<void> addChef(Chef chef) async {
    final db = await _databaseService.database;
    await db.insert('chefs', chef.toMap());
  }

  // تحديث شيف
  Future<void> updateChef(Chef chef) async {
    final db = await _databaseService.database;
    await db.update(
      'chefs',
      chef.toMap(),
      where: 'id = ?',
      whereArgs: [chef.id],
    );
  }

  // حذف شيف
  Future<void> deleteChef(String chefId) async {
    final db = await _databaseService.database;
    
    // حذف ربط الأصناف أولاً
    await db.delete(
      'chef_menu_assignments',
      where: 'chef_id = ?',
      whereArgs: [chefId],
    );
    
    // ثم حذف الشيف
    await db.delete(
      'chefs',
      where: 'id = ?',
      whereArgs: [chefId],
    );
  }

  // ربط صنف بشيف
  Future<void> assignMenuItemToChef(String chefId, String menuItemId, {int priority = 1}) async {
    final db = await _databaseService.database;
    
    final assignment = ChefMenuAssignment(
      id: '${chefId}_${menuItemId}_${DateTime.now().millisecondsSinceEpoch}',
      chefId: chefId,
      menuItemId: menuItemId,
      priority: priority,
      createdAt: DateTime.now(),
    );
    
    await db.insert('chef_menu_assignments', assignment.toMap());
  }

  // إلغاء ربط صنف من شيف
  Future<void> unassignMenuItemFromChef(String chefId, String menuItemId) async {
    final db = await _databaseService.database;
    
    await db.delete(
      'chef_menu_assignments',
      where: 'chef_id = ? AND menu_item_id = ?',
      whereArgs: [chefId, menuItemId],
    );
  }

  // الحصول على الشيف المسؤول عن صنف معين
  Future<Chef?> getChefForMenuItem(String menuItemId) async {
    final db = await _databaseService.database;
    
    final maps = await db.rawQuery('''
      SELECT c.* FROM chefs c
      INNER JOIN chef_menu_assignments cma ON c.id = cma.chef_id
      WHERE cma.menu_item_id = ? AND cma.is_active = 1 AND c.status = 'active'
      ORDER BY cma.priority ASC, c.priority ASC
      LIMIT 1
    ''', [menuItemId]);
    
    if (maps.isNotEmpty) {
      return Chef.fromMap(maps.first);
    }
    return null;
  }

  // الحصول على أصناف شيف معين
  Future<List<MenuItem>> getMenuItemsForChef(String chefId) async {
    final db = await _databaseService.database;
    
    final maps = await db.rawQuery('''
      SELECT m.* FROM menu_items m
      INNER JOIN chef_menu_assignments cma ON m.id = cma.menu_item_id
      WHERE cma.chef_id = ? AND cma.is_active = 1
      ORDER BY cma.priority ASC, m.name ASC
    ''', [chefId]);
    
    return maps.map((map) => MenuItem.fromMap(map)).toList();
  }
}
