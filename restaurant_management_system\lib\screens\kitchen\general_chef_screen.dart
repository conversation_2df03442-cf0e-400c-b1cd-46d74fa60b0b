import 'package:flutter/material.dart';
import '../../models/kitchen_order.dart';
import '../../models/user_role.dart';
import '../../widgets/kitchen/kitchen_order_card.dart';
import '../../services/kitchen_service.dart';
import '../../services/audio_service.dart';

class GeneralChefScreen extends StatefulWidget {
  const GeneralChefScreen({super.key});

  @override
  State<GeneralChefScreen> createState() => _GeneralChefScreenState();
}

class _GeneralChefScreenState extends State<GeneralChefScreen> {
  final KitchenService _kitchenService = KitchenService();
  final AudioService _audioService = AudioService();
  List<KitchenOrderItem> _generalOrders = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadGeneralOrders();
    _setupOrderListener();
  }

  void _loadGeneralOrders() async {
    setState(() => _isLoading = true);
    try {
      final orders = await _kitchenService.getOrdersByChef('general_chef');
      setState(() {
        _generalOrders = orders;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showError('خطأ في تحميل الطلبات: $e');
    }
  }

  void _setupOrderListener() {
    _kitchenService.onNewOrder.listen((order) {
      if (order.assignedChef == 'general_chef') {
        setState(() {
          _generalOrders.insert(0, order);
        });
        _audioService.playNewOrderSound();
        _showNewOrderNotification(order);
      }
    });
  }

  void _showNewOrderNotification(KitchenOrderItem order) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.restaurant, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'طلب جديد: ${order.menuItem.name} x${order.quantity}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.teal,
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'عرض',
          textColor: Colors.white,
          onPressed: () => _scrollToOrder(order.id),
        ),
      ),
    );
  }

  void _scrollToOrder(String orderId) {
    // تنفيذ التمرير للطلب المحدد
  }

  void _updateOrderStatus(KitchenOrderItem order, KitchenOrderStatus newStatus) async {
    try {
      await _kitchenService.updateOrderStatus(order.id, newStatus);
      setState(() {
        final index = _generalOrders.indexWhere((o) => o.id == order.id);
        if (index != -1) {
          _generalOrders[index] = order.copyWith(
            status: newStatus,
            startedAt: newStatus == KitchenOrderStatus.inProgress 
                ? DateTime.now() 
                : order.startedAt,
            completedAt: newStatus == KitchenOrderStatus.ready 
                ? DateTime.now() 
                : order.completedAt,
          );
        }
      });

      if (newStatus == KitchenOrderStatus.ready) {
        _audioService.playOrderReadySound();
      }
    } catch (e) {
      _showError('خطأ في تحديث حالة الطلب: $e');
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Row(
          children: [
            Icon(Icons.restaurant, color: Colors.white),
            SizedBox(width: 8),
            Text(
              'الشيف العام',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ],
        ),
        backgroundColor: Colors.teal,
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 16),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '${_generalOrders.where((o) => o.status != KitchenOrderStatus.completed).length}',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.teal,
                  ),
                ),
                const SizedBox(width: 4),
                const Text(
                  'طلب',
                  style: TextStyle(color: Colors.teal),
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadGeneralOrders,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildOrdersList(),
    );
  }

  Widget _buildOrdersList() {
    if (_generalOrders.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.restaurant,
              size: 80,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد طلبات حالياً',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    // تجميع الطلبات حسب الفئة
    final Map<String, List<KitchenOrderItem>> ordersByCategory = {};
    for (final order in _generalOrders) {
      final category = order.menuItem.category;
      if (!ordersByCategory.containsKey(category)) {
        ordersByCategory[category] = [];
      }
      ordersByCategory[category]!.add(order);
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عرض الطلبات مجمعة حسب الفئة
          ...ordersByCategory.entries.map((entry) {
            final category = entry.key;
            final orders = entry.value;
            
            final pendingOrders = orders
                .where((o) => o.status == KitchenOrderStatus.pending)
                .toList();
            final inProgressOrders = orders
                .where((o) => o.status == KitchenOrderStatus.inProgress)
                .toList();
            final readyOrders = orders
                .where((o) => o.status == KitchenOrderStatus.ready)
                .toList();

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // عنوان الفئة
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 8),
                  decoration: BoxDecoration(
                    color: Colors.teal.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.teal.withOpacity(0.3)),
                  ),
                  child: Text(
                    category,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.teal,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                // الطلبات الجديدة
                if (pendingOrders.isNotEmpty) ...[
                  _buildSectionHeader('طلبات جديدة', pendingOrders.length, Colors.blue),
                  const SizedBox(height: 8),
                  ...pendingOrders.map((order) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: KitchenOrderCard(
                      order: order,
                      onStatusUpdate: _updateOrderStatus,
                    ),
                  )),
                  const SizedBox(height: 16),
                ],

                // الطلبات قيد التحضير
                if (inProgressOrders.isNotEmpty) ...[
                  _buildSectionHeader('قيد التحضير', inProgressOrders.length, Colors.orange),
                  const SizedBox(height: 8),
                  ...inProgressOrders.map((order) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: KitchenOrderCard(
                      order: order,
                      onStatusUpdate: _updateOrderStatus,
                    ),
                  )),
                  const SizedBox(height: 16),
                ],

                // الطلبات الجاهزة
                if (readyOrders.isNotEmpty) ...[
                  _buildSectionHeader('جاهز للتقديم', readyOrders.length, Colors.green),
                  const SizedBox(height: 8),
                  ...readyOrders.map((order) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: KitchenOrderCard(
                      order: order,
                      onStatusUpdate: _updateOrderStatus,
                    ),
                  )),
                ],

                const SizedBox(height: 24),
              ],
            );
          }),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, int count, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              count.toString(),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
