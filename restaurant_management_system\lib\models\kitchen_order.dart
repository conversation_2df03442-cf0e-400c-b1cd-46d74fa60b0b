import 'order.dart';
import 'menu_item.dart';

enum KitchenOrderStatus {
  pending('جديد', 'New'),
  inProgress('قيد التحضير', 'In Progress'),
  ready('جاهز', 'Ready'),
  completed('مكتمل', 'Completed'),
  cancelled('ملغي', 'Cancelled');

  const KitchenOrderStatus(this.nameAr, this.nameEn);
  final String nameAr;
  final String nameEn;

  String get displayName => nameAr;
}

class KitchenOrderItem {
  final String id;
  final String orderId;
  final String menuItemId;
  final MenuItem menuItem;
  final int quantity;
  final String notes;
  final KitchenOrderStatus status;
  final String assignedChef; // نوع الشيف المخصص
  final DateTime createdAt;
  final DateTime? startedAt;
  final DateTime? completedAt;
  final int priority; // أولوية الطلب (1 = عالي، 5 = منخفض)

  KitchenOrderItem({
    required this.id,
    required this.orderId,
    required this.menuItemId,
    required this.menuItem,
    required this.quantity,
    this.notes = '',
    this.status = KitchenOrderStatus.pending,
    required this.assignedChef,
    required this.createdAt,
    this.startedAt,
    this.completedAt,
    this.priority = 3,
  });

  // تحديد نوع الشيف حسب الصنف
  static String getAssignedChef(String category) {
    switch (category.toLowerCase()) {
      case 'بيتزا':
      case 'pizza':
        return 'pizza_chef';
      case 'برجر':
      case 'burger':
      case 'ساندويتش':
      case 'sandwich':
        return 'burger_chef';
      default:
        return 'general_chef';
    }
  }

  // حساب الوقت المنقضي
  Duration get elapsedTime {
    final now = DateTime.now();
    if (startedAt != null) {
      return now.difference(startedAt!);
    }
    return now.difference(createdAt);
  }

  // حساب الوقت المتوقع للإنجاز
  Duration get estimatedTime {
    switch (menuItem.category.toLowerCase()) {
      case 'بيتزا':
      case 'pizza':
        return const Duration(minutes: 15);
      case 'برجر':
      case 'burger':
        return const Duration(minutes: 10);
      case 'مشروبات':
      case 'drinks':
        return const Duration(minutes: 2);
      default:
        return const Duration(minutes: 8);
    }
  }

  // التحقق من تأخر الطلب
  bool get isDelayed {
    return elapsedTime > estimatedTime;
  }

  // لون الحالة
  String get statusColor {
    switch (status) {
      case KitchenOrderStatus.pending:
        return isDelayed ? '#FF5722' : '#2196F3'; // أحمر إذا متأخر، أزرق إذا جديد
      case KitchenOrderStatus.inProgress:
        return isDelayed ? '#FF9800' : '#4CAF50'; // برتقالي إذا متأخر، أخضر إذا طبيعي
      case KitchenOrderStatus.ready:
        return '#9C27B0'; // بنفسجي
      case KitchenOrderStatus.completed:
        return '#607D8B'; // رمادي
      case KitchenOrderStatus.cancelled:
        return '#F44336'; // أحمر
    }
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'order_id': orderId,
      'menu_item_id': menuItemId,
      'quantity': quantity,
      'notes': notes,
      'status': status.name,
      'assigned_chef': assignedChef,
      'created_at': createdAt.toIso8601String(),
      'started_at': startedAt?.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
      'priority': priority,
    };
  }

  factory KitchenOrderItem.fromMap(Map<String, dynamic> map, MenuItem menuItem) {
    return KitchenOrderItem(
      id: map['id'],
      orderId: map['order_id'],
      menuItemId: map['menu_item_id'],
      menuItem: menuItem,
      quantity: map['quantity'],
      notes: map['notes'] ?? '',
      status: KitchenOrderStatus.values.firstWhere(
        (s) => s.name == map['status'],
        orElse: () => KitchenOrderStatus.pending,
      ),
      assignedChef: map['assigned_chef'],
      createdAt: DateTime.parse(map['created_at']),
      startedAt: map['started_at'] != null 
          ? DateTime.parse(map['started_at']) 
          : null,
      completedAt: map['completed_at'] != null 
          ? DateTime.parse(map['completed_at']) 
          : null,
      priority: map['priority'] ?? 3,
    );
  }

  KitchenOrderItem copyWith({
    String? id,
    String? orderId,
    String? menuItemId,
    MenuItem? menuItem,
    int? quantity,
    String? notes,
    KitchenOrderStatus? status,
    String? assignedChef,
    DateTime? createdAt,
    DateTime? startedAt,
    DateTime? completedAt,
    int? priority,
  }) {
    return KitchenOrderItem(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      menuItemId: menuItemId ?? this.menuItemId,
      menuItem: menuItem ?? this.menuItem,
      quantity: quantity ?? this.quantity,
      notes: notes ?? this.notes,
      status: status ?? this.status,
      assignedChef: assignedChef ?? this.assignedChef,
      createdAt: createdAt ?? this.createdAt,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      priority: priority ?? this.priority,
    );
  }
}

class KitchenOrder {
  final String orderId;
  final int tableNumber;
  final OrderType orderType;
  final List<KitchenOrderItem> items;
  final DateTime createdAt;
  final String customerName;
  final int totalItems;

  KitchenOrder({
    required this.orderId,
    required this.tableNumber,
    required this.orderType,
    required this.items,
    required this.createdAt,
    this.customerName = '',
    required this.totalItems,
  });

  // حالة الطلب الإجمالية
  KitchenOrderStatus get overallStatus {
    if (items.isEmpty) return KitchenOrderStatus.pending;
    
    if (items.every((item) => item.status == KitchenOrderStatus.completed)) {
      return KitchenOrderStatus.completed;
    } else if (items.every((item) => item.status == KitchenOrderStatus.cancelled)) {
      return KitchenOrderStatus.cancelled;
    } else if (items.any((item) => item.status == KitchenOrderStatus.ready)) {
      return KitchenOrderStatus.ready;
    } else if (items.any((item) => item.status == KitchenOrderStatus.inProgress)) {
      return KitchenOrderStatus.inProgress;
    } else {
      return KitchenOrderStatus.pending;
    }
  }

  // عدد الأصناف المكتملة
  int get completedItemsCount {
    return items.where((item) => 
        item.status == KitchenOrderStatus.completed ||
        item.status == KitchenOrderStatus.ready
    ).length;
  }

  // نسبة الإنجاز
  double get completionPercentage {
    if (items.isEmpty) return 0.0;
    return completedItemsCount / items.length;
  }

  // التحقق من تأخر الطلب
  bool get isDelayed {
    return items.any((item) => item.isDelayed);
  }

  // الوقت المتوقع للإنجاز
  Duration get estimatedCompletionTime {
    if (items.isEmpty) return Duration.zero;
    
    final maxTime = items
        .map((item) => item.estimatedTime)
        .reduce((a, b) => a > b ? a : b);
    
    return maxTime;
  }
}
