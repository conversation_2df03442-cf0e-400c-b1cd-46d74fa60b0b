import 'package:flutter/material.dart';

// نموذج الشيفات المرن
class Chef {
  final String id;
  final String name;
  final String username;
  final String password; // كلمة المرور
  final String specialty; // التخصص (مرن)
  final String? description;
  final String color; // لون مميز للشيف
  final ChefStatus status;
  final List<String> workingDays; // أيام العمل
  final String startTime; // وقت بداية العمل
  final String endTime; // وقت نهاية العمل
  final List<String> assignedCategories; // المجموعات المخصصة للشيف
  final List<String> assignedMenuItems; // الأصناف المخصصة للشيف
  final String? deviceId; // معرف الجهاز المخصص
  final bool receiveNotifications;
  final int priority; // أولوية الشيف (1 = أعلى)
  final DateTime createdAt;
  final DateTime? lastLogin;

  Chef({
    required this.id,
    required this.name,
    required this.username,
    required this.password,
    required this.specialty,
    this.description,
    required this.color,
    this.status = ChefStatus.active,
    required this.workingDays,
    required this.startTime,
    required this.endTime,
    this.assignedCategories = const [],
    this.assignedMenuItems = const [],
    this.deviceId,
    this.receiveNotifications = true,
    this.priority = 5,
    required this.createdAt,
    this.lastLogin,
  });

  // التحقق من أن الشيف يعمل الآن
  bool get isWorkingNow {
    if (status != ChefStatus.active) return false;

    final now = DateTime.now();
    final currentDay = _getDayName(now.weekday);

    if (!workingDays.contains(currentDay)) return false;

    final currentTime = TimeOfDay.fromDateTime(now);
    final start = _parseTime(startTime);
    final end = _parseTime(endTime);

    return _isTimeBetween(currentTime, start, end);
  }

  // الحصول على اسم اليوم
  String _getDayName(int weekday) {
    const days = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد'
    ];
    return days[weekday - 1];
  }

  // تحويل النص لوقت
  TimeOfDay _parseTime(String timeString) {
    final parts = timeString.split(':');
    return TimeOfDay(
      hour: int.parse(parts[0]),
      minute: int.parse(parts[1]),
    );
  }

  // التحقق من أن الوقت بين وقتين
  bool _isTimeBetween(TimeOfDay current, TimeOfDay start, TimeOfDay end) {
    final currentMinutes = current.hour * 60 + current.minute;
    final startMinutes = start.hour * 60 + start.minute;
    final endMinutes = end.hour * 60 + end.minute;

    if (startMinutes <= endMinutes) {
      return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
    } else {
      // العمل عبر منتصف الليل
      return currentMinutes >= startMinutes || currentMinutes <= endMinutes;
    }
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'username': username,
      'password': password,
      'specialty': specialty,
      'description': description,
      'color': color,
      'status': status.name,
      'working_days': workingDays.join(','),
      'start_time': startTime,
      'end_time': endTime,
      'assigned_categories': assignedCategories.join(','),
      'assigned_menu_items': assignedMenuItems.join(','),
      'device_id': deviceId,
      'receive_notifications': receiveNotifications ? 1 : 0,
      'priority': priority,
      'created_at': createdAt.toIso8601String(),
      'last_login': lastLogin?.toIso8601String(),
    };
  }

  factory Chef.fromMap(Map<String, dynamic> map) {
    return Chef(
      id: map['id'],
      name: map['name'],
      username: map['username'],
      password: map['password'] ?? '123456',
      specialty: map['specialty'],
      description: map['description'],
      color: map['color'],
      status: ChefStatus.values.firstWhere(
        (s) => s.name == map['status'],
        orElse: () => ChefStatus.active,
      ),
      workingDays: map['working_days']?.split(',') ?? [],
      startTime: map['start_time'] ?? '09:00',
      endTime: map['end_time'] ?? '22:00',
      assignedCategories: map['assigned_categories']
              ?.split(',')
              .where((s) => s.isNotEmpty)
              .toList() ??
          [],
      assignedMenuItems: map['assigned_menu_items']
              ?.split(',')
              .where((s) => s.isNotEmpty)
              .toList() ??
          [],
      deviceId: map['device_id'],
      receiveNotifications: map['receive_notifications'] == 1,
      priority: map['priority'] ?? 5,
      createdAt: DateTime.parse(map['created_at']),
      lastLogin:
          map['last_login'] != null ? DateTime.parse(map['last_login']) : null,
    );
  }

  Chef copyWith({
    String? id,
    String? name,
    String? username,
    String? password,
    String? specialty,
    String? description,
    String? color,
    ChefStatus? status,
    List<String>? workingDays,
    String? startTime,
    String? endTime,
    List<String>? assignedCategories,
    List<String>? assignedMenuItems,
    String? deviceId,
    bool? receiveNotifications,
    int? priority,
    DateTime? createdAt,
    DateTime? lastLogin,
  }) {
    return Chef(
      id: id ?? this.id,
      name: name ?? this.name,
      username: username ?? this.username,
      password: password ?? this.password,
      specialty: specialty ?? this.specialty,
      description: description ?? this.description,
      color: color ?? this.color,
      status: status ?? this.status,
      workingDays: workingDays ?? this.workingDays,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      assignedCategories: assignedCategories ?? this.assignedCategories,
      assignedMenuItems: assignedMenuItems ?? this.assignedMenuItems,
      deviceId: deviceId ?? this.deviceId,
      receiveNotifications: receiveNotifications ?? this.receiveNotifications,
      priority: priority ?? this.priority,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
    );
  }
}

enum ChefStatus {
  active('نشط', 'Active'),
  inactive('غير نشط', 'Inactive'),
  onBreak('في استراحة', 'On Break'),
  offline('غير متصل', 'Offline');

  const ChefStatus(this.nameAr, this.nameEn);
  final String nameAr;
  final String nameEn;

  String get displayName => nameAr;
}

// ربط الأصناف بالشيفات
class ChefMenuAssignment {
  final String id;
  final String chefId;
  final String menuItemId;
  final int priority; // أولوية الشيف لهذا الصنف
  final bool isActive;
  final DateTime createdAt;

  ChefMenuAssignment({
    required this.id,
    required this.chefId,
    required this.menuItemId,
    this.priority = 1,
    this.isActive = true,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'chef_id': chefId,
      'menu_item_id': menuItemId,
      'priority': priority,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory ChefMenuAssignment.fromMap(Map<String, dynamic> map) {
    return ChefMenuAssignment(
      id: map['id'],
      chefId: map['chef_id'],
      menuItemId: map['menu_item_id'],
      priority: map['priority'] ?? 1,
      isActive: map['is_active'] == 1,
      createdAt: DateTime.parse(map['created_at']),
    );
  }
}
