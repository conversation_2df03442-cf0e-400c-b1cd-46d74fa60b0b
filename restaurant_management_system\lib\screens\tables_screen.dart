import 'package:flutter/material.dart';
import '../models/table.dart';
import '../services/table_service.dart';

class TablesScreen extends StatefulWidget {
  const TablesScreen({super.key});

  @override
  State<TablesScreen> createState() => _TablesScreenState();
}

class _TablesScreenState extends State<TablesScreen> {
  final TableService _tableService = TableService();
  List<RestaurantTable> _tables = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadTables();
  }

  Future<void> _loadTables() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final tables = await _tableService.getAllTables();
      setState(() {
        _tables = tables;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الطاولات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _updateTableStatus(RestaurantTable table, TableStatus newStatus) async {
    try {
      final updatedTable = table.copyWith(status: newStatus);
      await _tableService.updateTable(updatedTable);
      await _loadTables();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث حالة الطاولة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث حالة الطاولة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Color _getTableColor(TableStatus status) {
    switch (status) {
      case TableStatus.available:
        return Colors.green;
      case TableStatus.occupied:
        return Colors.red;
      case TableStatus.reserved:
        return Colors.orange;
      case TableStatus.outOfService:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الطاولات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadTables,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _tables.isEmpty
              ? const Center(
                  child: Text(
                    'لا توجد طاولات',
                    style: TextStyle(fontSize: 18),
                  ),
                )
              : Column(
                  children: [
                    // مفتاح الألوان
                    Container(
                      padding: const EdgeInsets.all(16),
                      child: Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'حالة الطاولات:',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(height: 12),
                              Wrap(
                                spacing: 16,
                                runSpacing: 8,
                                children: [
                                  _buildStatusIndicator(
                                    Colors.green,
                                    'متاحة',
                                  ),
                                  _buildStatusIndicator(
                                    Colors.red,
                                    'مشغولة',
                                  ),
                                  _buildStatusIndicator(
                                    Colors.orange,
                                    'محجوزة',
                                  ),
                                  _buildStatusIndicator(
                                    Colors.grey,
                                    'خارج الخدمة',
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    
                    // شبكة الطاولات
                    Expanded(
                      child: GridView.builder(
                        padding: const EdgeInsets.all(16),
                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 4,
                          crossAxisSpacing: 16,
                          mainAxisSpacing: 16,
                          childAspectRatio: 1,
                        ),
                        itemCount: _tables.length,
                        itemBuilder: (context, index) {
                          final table = _tables[index];
                          return _buildTableCard(table);
                        },
                      ),
                    ),
                  ],
                ),
    );
  }

  Widget _buildStatusIndicator(Color color, String label) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Text(label),
      ],
    );
  }

  Widget _buildTableCard(RestaurantTable table) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: () => _showTableDialog(table),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _getTableColor(table.status),
              width: 3,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.table_restaurant,
                size: 40,
                color: _getTableColor(table.status),
              ),
              const SizedBox(height: 8),
              Text(
                'طاولة ${table.number}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '${table.capacity} أشخاص',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 4),
              Text(
                table.status.displayName,
                style: TextStyle(
                  fontSize: 12,
                  color: _getTableColor(table.status),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showTableDialog(RestaurantTable table) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('طاولة ${table.number}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('السعة: ${table.capacity} أشخاص'),
            const SizedBox(height: 8),
            Text('الحالة: ${table.status.displayName}'),
            if (table.location != null) ...[
              const SizedBox(height: 8),
              Text('الموقع: ${table.location}'),
            ],
            if (table.notes != null) ...[
              const SizedBox(height: 8),
              Text('ملاحظات: ${table.notes}'),
            ],
            const SizedBox(height: 16),
            const Text(
              'تغيير الحالة:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: TableStatus.values.map((status) {
                return ElevatedButton(
                  onPressed: table.status != status
                      ? () {
                          Navigator.of(context).pop();
                          _updateTableStatus(table, status);
                        }
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _getTableColor(status),
                    foregroundColor: Colors.white,
                  ),
                  child: Text(
                    status.displayName,
                    style: const TextStyle(fontSize: 12),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
