import '../models/menu_item.dart';
import '../models/order.dart';
import '../models/table.dart';

class MockDataService {
  static final MockDataService _instance = MockDataService._internal();
  factory MockDataService() => _instance;
  MockDataService._internal();

  // بيانات تجريبية للأصناف
  static List<MenuItem> get mockMenuItems => [
    MenuItem(
      id: 1,
      name: 'شاورما لحم',
      nameEn: 'Beef Shawarma',
      description: 'شاورما لحم طازجة مع الخضار والصلصة',
      descriptionEn: 'Fresh beef shawarma with vegetables and sauce',
      price: 25.0,
      category: 'أطباق رئيسية',
      imagePath: 'https://images.unsplash.com/photo-1529006557810-274b9b2fc783?w=400&h=300&fit=crop',
      isAvailable: true,
    ),
    MenuItem(
      id: 2,
      name: 'شاورما دجاج',
      nameEn: 'Chicken Shawarma',
      description: 'شاورما دجاج طازجة مع الخضار والصلصة',
      descriptionEn: 'Fresh chicken shawarma with vegetables and sauce',
      price: 20.0,
      category: 'أطباق رئيسية',
      imagePath: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop',
      isAvailable: true,
    ),
    MenuItem(
      id: 3,
      name: 'فلافل',
      nameEn: 'Falafel',
      description: 'فلافل طازج مع الطحينة والخضار',
      descriptionEn: 'Fresh falafel with tahini and vegetables',
      price: 15.0,
      category: 'أطباق رئيسية',
      imagePath: 'https://images.unsplash.com/photo-1593504049359-74330189a345?w=400&h=300&fit=crop',
      isAvailable: true,
    ),
    MenuItem(
      id: 4,
      name: 'حمص',
      nameEn: 'Hummus',
      description: 'حمص طازج مع زيت الزيتون',
      descriptionEn: 'Fresh hummus with olive oil',
      price: 12.0,
      category: 'مقبلات',
      imagePath: 'https://images.unsplash.com/photo-1571197119282-7c4a2b8a4b8b?w=400&h=300&fit=crop',
      isAvailable: true,
    ),
    MenuItem(
      id: 5,
      name: 'تبولة',
      nameEn: 'Tabbouleh',
      description: 'سلطة تبولة طازجة',
      descriptionEn: 'Fresh tabbouleh salad',
      price: 18.0,
      category: 'سلطات',
      imagePath: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop',
      isAvailable: true,
    ),
    MenuItem(
      id: 6,
      name: 'عصير برتقال',
      nameEn: 'Orange Juice',
      description: 'عصير برتقال طازج',
      descriptionEn: 'Fresh orange juice',
      price: 8.0,
      category: 'مشروبات',
      imagePath: 'https://images.unsplash.com/photo-1621506289937-a8e4df240d0b?w=400&h=300&fit=crop',
      isAvailable: true,
    ),
    MenuItem(
      id: 7,
      name: 'شاي',
      nameEn: 'Tea',
      description: 'شاي أحمر',
      descriptionEn: 'Black tea',
      price: 3.0,
      category: 'مشروبات',
      imagePath: 'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=400&h=300&fit=crop',
      isAvailable: true,
    ),
    MenuItem(
      id: 8,
      name: 'قهوة عربية',
      nameEn: 'Arabic Coffee',
      description: 'قهوة عربية أصيلة',
      descriptionEn: 'Authentic Arabic coffee',
      price: 5.0,
      category: 'مشروبات',
      imagePath: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=400&h=300&fit=crop',
      isAvailable: true,
    ),
    MenuItem(
      id: 9,
      name: 'برجر لحم',
      nameEn: 'Beef Burger',
      description: 'برجر لحم مشوي مع البطاطس',
      descriptionEn: 'Grilled beef burger with fries',
      price: 30.0,
      category: 'أطباق رئيسية',
      imagePath: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=400&h=300&fit=crop',
      isAvailable: true,
    ),
    MenuItem(
      id: 10,
      name: 'بيتزا مارجريتا',
      nameEn: 'Margherita Pizza',
      description: 'بيتزا مارجريتا كلاسيكية',
      descriptionEn: 'Classic margherita pizza',
      price: 35.0,
      category: 'أطباق رئيسية',
      imagePath: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop',
      isAvailable: true,
    ),
    MenuItem(
      id: 11,
      name: 'سلطة قيصر',
      nameEn: 'Caesar Salad',
      description: 'سلطة قيصر مع الدجاج المشوي',
      descriptionEn: 'Caesar salad with grilled chicken',
      price: 22.0,
      category: 'سلطات',
      imagePath: 'https://images.unsplash.com/photo-1546793665-c74683f339c1?w=400&h=300&fit=crop',
      isAvailable: true,
    ),
    MenuItem(
      id: 12,
      name: 'كنافة',
      nameEn: 'Kunafa',
      description: 'كنافة بالجبن والقطر',
      descriptionEn: 'Kunafa with cheese and syrup',
      price: 15.0,
      category: 'حلويات',
      imagePath: 'https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400&h=300&fit=crop',
      isAvailable: true,
    ),
  ];

  // بيانات تجريبية للطاولات
  static List<RestaurantTable> get mockTables => List.generate(
    15,
    (index) => RestaurantTable(
      id: index + 1,
      number: index + 1,
      capacity: (index % 3 == 0) ? 4 : (index % 3 == 1) ? 6 : 8,
      status: index < 3 ? TableStatus.occupied : 
              index < 6 ? TableStatus.available :
              index < 8 ? TableStatus.reserved : TableStatus.available,
      location: index < 8 ? 'الطابق الأول' : 'الطابق الثاني',
    ),
  );

  // الفئات المتاحة
  static List<String> get mockCategories => [
    'أطباق رئيسية',
    'مقبلات',
    'سلطات',
    'مشروبات',
    'حلويات',
  ];

  // طلبات تجريبية
  static List<Order> get mockOrders => [
    Order(
      id: 1,
      orderNumber: '20241201001',
      type: OrderType.dineIn,
      status: OrderStatus.preparing,
      tableNumber: 5,
      items: [
        OrderItem(
          id: 1,
          menuItemId: 1,
          menuItem: mockMenuItems[0],
          quantity: 2,
          unitPrice: 25.0,
        ),
        OrderItem(
          id: 2,
          menuItemId: 6,
          menuItem: mockMenuItems[5],
          quantity: 2,
          unitPrice: 8.0,
        ),
      ],
      subtotal: 66.0,
      taxAmount: 9.9,
      totalAmount: 75.9,
      createdAt: DateTime.now().subtract(const Duration(minutes: 15)),
    ),
    Order(
      id: 2,
      orderNumber: '20241201002',
      type: OrderType.takeaway,
      status: OrderStatus.ready,
      customerName: 'أحمد محمد',
      customerPhone: '0501234567',
      items: [
        OrderItem(
          id: 3,
          menuItemId: 2,
          menuItem: mockMenuItems[1],
          quantity: 1,
          unitPrice: 20.0,
        ),
        OrderItem(
          id: 4,
          menuItemId: 7,
          menuItem: mockMenuItems[6],
          quantity: 1,
          unitPrice: 3.0,
        ),
      ],
      subtotal: 23.0,
      taxAmount: 3.45,
      totalAmount: 26.45,
      createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
    ),
  ];
}
