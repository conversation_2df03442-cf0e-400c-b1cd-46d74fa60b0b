enum UserRole {
  manager('مدير', 'Manager'),
  cashier('كاشير', 'Cashier'),
  waiter('نادل', 'Waiter'),
  pizza<PERSON>he<PERSON>('شيف البيتزا', 'Pizza Chef'),
  burger<PERSON><PERSON><PERSON>('شيف البرجر', 'Burger Chef'),
  general<PERSON><PERSON><PERSON>('شيف عام', 'General Chef'),
  kitchen<PERSON><PERSON>ger('مدير المطبخ', 'Kitchen Manager');

  const UserRole(this.nameAr, this.nameEn);

  final String nameAr;
  final String nameEn;

  String get displayName => nameAr;

  // الصلاحيات لكل دور
  List<Permission> get permissions {
    switch (this) {
      case UserRole.manager:
        return Permission.values; // جميع الصلاحيات
      case UserRole.cashier:
        return [
          Permission.createOrder,
          Permission.viewOrders,
          Permission.printReceipt,
          Permission.managePayment,
        ];
      case UserRole.waiter:
        return [
          Permission.createOrder,
          Permission.viewOrders,
          Permission.manageTables,
          Permission.viewKitchenStatus,
        ];
      case UserRole.pizzaChef:
        return [
          Permission.viewKitchenOrders,
          Permission.updateOrderStatus,
          Permission.viewPizzaOrders,
        ];
      case UserRole.burgerChef:
        return [
          Permission.viewKitchenOrders,
          Permission.updateOrderStatus,
          Permission.viewBurgerOrders,
        ];
      case UserRole.generalChef:
        return [
          Permission.viewKitchenOrders,
          Permission.updateOrderStatus,
          Permission.viewGeneralOrders,
        ];
      case UserRole.kitchenManager:
        return [
          Permission.viewKitchenOrders,
          Permission.updateOrderStatus,
          Permission.viewAllKitchenOrders,
          Permission.manageKitchenStaff,
        ];
    }
  }

  // الشاشة الرئيسية لكل دور
  String get homeRoute {
    switch (this) {
      case UserRole.manager:
        return '/dashboard';
      case UserRole.cashier:
        return '/pos';
      case UserRole.waiter:
        return '/waiter';
      case UserRole.pizzaChef:
        return '/kitchen/pizza';
      case UserRole.burgerChef:
        return '/kitchen/burger';
      case UserRole.generalChef:
        return '/kitchen/general';
      case UserRole.kitchenManager:
        return '/kitchen/manager';
    }
  }

  // الأصناف المخصصة لكل شيف
  List<String> get assignedCategories {
    switch (this) {
      case UserRole.pizzaChef:
        return ['بيتزا', 'Pizza'];
      case UserRole.burgerChef:
        return ['برجر', 'Burger', 'ساندويتش'];
      case UserRole.generalChef:
        return ['أطباق رئيسية', 'مقبلات', 'سلطات', 'مشروبات', 'حلويات'];
      default:
        return [];
    }
  }
}

enum Permission {
  // صلاحيات الطلبات
  createOrder('إنشاء طلب'),
  viewOrders('عرض الطلبات'),
  editOrder('تعديل طلب'),
  cancelOrder('إلغاء طلب'),
  
  // صلاحيات المطبخ
  viewKitchenOrders('عرض طلبات المطبخ'),
  updateOrderStatus('تحديث حالة الطلب'),
  viewPizzaOrders('عرض طلبات البيتزا'),
  viewBurgerOrders('عرض طلبات البرجر'),
  viewGeneralOrders('عرض الطلبات العامة'),
  viewAllKitchenOrders('عرض جميع طلبات المطبخ'),
  
  // صلاحيات الطاولات
  manageTables('إدارة الطاولات'),
  viewTables('عرض الطاولات'),
  
  // صلاحيات الدفع والطباعة
  managePayment('إدارة الدفع'),
  printReceipt('طباعة الفاتورة'),
  
  // صلاحيات إدارية
  manageMenu('إدارة القائمة'),
  manageUsers('إدارة المستخدمين'),
  viewReports('عرض التقارير'),
  manageSettings('إدارة الإعدادات'),
  manageKitchenStaff('إدارة طاقم المطبخ'),
  
  // صلاحيات عامة
  viewKitchenStatus('عرض حالة المطبخ');

  const Permission(this.nameAr);
  final String nameAr;
}

class User {
  final String id;
  final String username;
  final String name;
  final UserRole role;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? lastLogin;

  User({
    required this.id,
    required this.username,
    required this.name,
    required this.role,
    this.isActive = true,
    required this.createdAt,
    this.lastLogin,
  });

  // التحقق من الصلاحية
  bool hasPermission(Permission permission) {
    return role.permissions.contains(permission);
  }

  // التحقق من إمكانية رؤية صنف معين
  bool canViewCategory(String category) {
    if (role.assignedCategories.isEmpty) return true;
    return role.assignedCategories.contains(category);
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'username': username,
      'name': name,
      'role': role.name,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'last_login': lastLogin?.toIso8601String(),
    };
  }

  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id'],
      username: map['username'],
      name: map['name'],
      role: UserRole.values.firstWhere((r) => r.name == map['role']),
      isActive: map['is_active'] == 1,
      createdAt: DateTime.parse(map['created_at']),
      lastLogin: map['last_login'] != null 
          ? DateTime.parse(map['last_login']) 
          : null,
    );
  }

  User copyWith({
    String? id,
    String? username,
    String? name,
    UserRole? role,
    bool? isActive,
    DateTime? createdAt,
    DateTime? lastLogin,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      name: name ?? this.name,
      role: role ?? this.role,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
    );
  }
}
