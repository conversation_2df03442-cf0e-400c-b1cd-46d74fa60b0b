import 'package:flutter/material.dart';
import '../../models/chef.dart';
import '../../services/restaurant_config_service.dart';

class ChefsManagementScreen extends StatefulWidget {
  const ChefsManagementScreen({super.key});

  @override
  State<ChefsManagementScreen> createState() => _ChefsManagementScreenState();
}

class _ChefsManagementScreenState extends State<ChefsManagementScreen> {
  final RestaurantConfigService _configService = RestaurantConfigService();
  List<Chef> _chefs = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadChefs();
  }

  Future<void> _loadChefs() async {
    setState(() => _isLoading = true);
    try {
      final chefs = await _configService.getAllChefs();
      setState(() {
        _chefs = chefs;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showError('خطأ في تحميل الشيفات: $e');
    }
  }

  void _showAddChefDialog() {
    showDialog(
      context: context,
      builder: (context) => AddChefDialog(
        onChefAdded: (chef) async {
          try {
            await _configService.addChef(chef);
            _loadChefs();
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إضافة الشيف بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          } catch (e) {
            _showError('خطأ في إضافة الشيف: $e');
          }
        },
      ),
    );
  }

  void _showEditChefDialog(Chef chef) {
    showDialog(
      context: context,
      builder: (context) => EditChefDialog(
        chef: chef,
        onChefUpdated: (updatedChef) async {
          try {
            await _configService.updateChef(updatedChef);
            _loadChefs();
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم تحديث الشيف بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          } catch (e) {
            _showError('خطأ في تحديث الشيف: $e');
          }
        },
      ),
    );
  }

  void _deleteChef(Chef chef) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الشيف'),
        content: Text(
            'هل أنت متأكد من حذف الشيف ${chef.name}؟\nسيتم حذف جميع ربط الأصناف المرتبطة به.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await _configService.deleteChef(chef.id);
                _loadChefs();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف الشيف بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                _showError('خطأ في حذف الشيف: $e');
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'إدارة الشيفات',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showAddChefDialog,
            icon: const Icon(Icons.add),
          ),
          IconButton(
            onPressed: _loadChefs,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // إحصائيات سريعة
                Container(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      _buildStatCard(
                        'إجمالي الشيفات',
                        _chefs.length.toString(),
                        Colors.blue,
                      ),
                      const SizedBox(width: 8),
                      _buildStatCard(
                        'نشط',
                        _chefs
                            .where((c) => c.status == ChefStatus.active)
                            .length
                            .toString(),
                        Colors.green,
                      ),
                      const SizedBox(width: 8),
                      _buildStatCard(
                        'يعمل الآن',
                        _chefs.where((c) => c.isWorkingNow).length.toString(),
                        Colors.orange,
                      ),
                    ],
                  ),
                ),

                // قائمة الشيفات
                Expanded(
                  child: _chefs.isEmpty
                      ? const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.person_outline,
                                  size: 64, color: Colors.grey),
                              SizedBox(height: 16),
                              Text(
                                'لا يوجد شيفات',
                                style:
                                    TextStyle(fontSize: 18, color: Colors.grey),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: _chefs.length,
                          itemBuilder: (context, index) {
                            final chef = _chefs[index];
                            return _buildChefCard(chef);
                          },
                        ),
                ),
              ],
            ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChefCard(Chef chef) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor:
              Color(int.parse(chef.color.substring(1), radix: 16) + 0xFF000000),
          child: Text(
            chef.name.isNotEmpty ? chef.name[0] : '؟',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          chef.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(chef.specialty),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  chef.isWorkingNow ? Icons.circle : Icons.circle_outlined,
                  size: 12,
                  color: chef.isWorkingNow ? Colors.green : Colors.grey,
                ),
                const SizedBox(width: 4),
                Text(
                  chef.isWorkingNow ? 'يعمل الآن' : 'غير متاح',
                  style: TextStyle(
                    fontSize: 12,
                    color: chef.isWorkingNow ? Colors.green : Colors.grey,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  '${chef.startTime} - ${chef.endTime}',
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getStatusColor(chef.status),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                chef.status.displayName,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            PopupMenuButton(
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit),
                      SizedBox(width: 8),
                      Text('تعديل'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: Colors.red),
                      SizedBox(width: 8),
                      Text('حذف', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
              onSelected: (value) {
                if (value == 'edit') {
                  _showEditChefDialog(chef);
                } else if (value == 'delete') {
                  _deleteChef(chef);
                }
              },
            ),
          ],
        ),
        onTap: () => _showEditChefDialog(chef),
      ),
    );
  }

  Color _getStatusColor(ChefStatus status) {
    switch (status) {
      case ChefStatus.active:
        return Colors.green;
      case ChefStatus.inactive:
        return Colors.grey;
      case ChefStatus.onBreak:
        return Colors.orange;
      case ChefStatus.offline:
        return Colors.red;
    }
  }
}

// Dialog لإضافة شيف جديد
class AddChefDialog extends StatefulWidget {
  final Function(Chef) onChefAdded;

  const AddChefDialog({super.key, required this.onChefAdded});

  @override
  State<AddChefDialog> createState() => _AddChefDialogState();
}

class _AddChefDialogState extends State<AddChefDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController(text: '123456');
  final _specialtyController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _startTimeController = TextEditingController(text: '09:00');
  final _endTimeController = TextEditingController(text: '18:00');

  ChefStatus _selectedStatus = ChefStatus.active;
  String _selectedColor = '#FF9800';
  List<String> _selectedDays = [];
  int _priority = 5;

  final List<String> _availableDays = [
    'الأحد',
    'الاثنين',
    'الثلاثاء',
    'الأربعاء',
    'الخميس',
    'الجمعة',
    'السبت'
  ];

  final List<String> _availableColors = [
    '#FF9800',
    '#2196F3',
    '#4CAF50',
    '#F44336',
    '#9C27B0',
    '#FF5722',
    '#795548',
    '#607D8B'
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _specialtyController.dispose();
    _descriptionController.dispose();
    _startTimeController.dispose();
    _endTimeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إضافة شيف جديد'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'اسم الشيف',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال اسم الشيف';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _usernameController,
                decoration: const InputDecoration(
                  labelText: 'اسم المستخدم',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال اسم المستخدم';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _passwordController,
                decoration: const InputDecoration(
                  labelText: 'كلمة المرور',
                  border: OutlineInputBorder(),
                ),
                obscureText: true,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال كلمة المرور';
                  }
                  if (value.length < 6) {
                    return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _specialtyController,
                decoration: const InputDecoration(
                  labelText: 'التخصص',
                  border: OutlineInputBorder(),
                  hintText: 'شيف البيتزا، شيف البرجر، إلخ',
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال التخصص';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'الوصف (اختياري)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 16),

              // اختيار اللون
              const Text('اللون المميز:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: _availableColors.map((color) {
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedColor = color;
                      });
                    },
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Color(int.parse(color.substring(1), radix: 16) +
                            0xFF000000),
                        shape: BoxShape.circle,
                        border: _selectedColor == color
                            ? Border.all(color: Colors.black, width: 3)
                            : null,
                      ),
                    ),
                  );
                }).toList(),
              ),

              const SizedBox(height: 16),

              // أيام العمل
              const Text('أيام العمل:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: _availableDays.map((day) {
                  return FilterChip(
                    label: Text(day),
                    selected: _selectedDays.contains(day),
                    onSelected: (selected) {
                      setState(() {
                        if (selected) {
                          _selectedDays.add(day);
                        } else {
                          _selectedDays.remove(day);
                        }
                      });
                    },
                  );
                }).toList(),
              ),

              const SizedBox(height: 16),

              // أوقات العمل
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _startTimeController,
                      decoration: const InputDecoration(
                        labelText: 'وقت البداية',
                        border: OutlineInputBorder(),
                        hintText: '09:00',
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _endTimeController,
                      decoration: const InputDecoration(
                        labelText: 'وقت النهاية',
                        border: OutlineInputBorder(),
                        hintText: '18:00',
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              if (_selectedDays.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('يرجى اختيار أيام العمل'),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              final chef = Chef(
                id: 'chef_${DateTime.now().millisecondsSinceEpoch}',
                name: _nameController.text.trim(),
                username: _usernameController.text.trim(),
                password: _passwordController.text.trim(),
                specialty: _specialtyController.text.trim(),
                description: _descriptionController.text.trim().isEmpty
                    ? null
                    : _descriptionController.text.trim(),
                color: _selectedColor,
                status: _selectedStatus,
                workingDays: _selectedDays,
                startTime: _startTimeController.text.trim(),
                endTime: _endTimeController.text.trim(),
                priority: _priority,
                createdAt: DateTime.now(),
              );

              widget.onChefAdded(chef);
              Navigator.pop(context);
            }
          },
          child: const Text('إضافة'),
        ),
      ],
    );
  }
}

// Dialog لتعديل شيف
class EditChefDialog extends StatefulWidget {
  final Chef chef;
  final Function(Chef) onChefUpdated;

  const EditChefDialog({
    super.key,
    required this.chef,
    required this.onChefUpdated,
  });

  @override
  State<EditChefDialog> createState() => _EditChefDialogState();
}

class _EditChefDialogState extends State<EditChefDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _usernameController;
  late final TextEditingController _specialtyController;
  late final TextEditingController _descriptionController;
  late final TextEditingController _startTimeController;
  late final TextEditingController _endTimeController;

  late ChefStatus _selectedStatus;
  late String _selectedColor;
  late List<String> _selectedDays;
  late int _priority;

  final List<String> _availableDays = [
    'الأحد',
    'الاثنين',
    'الثلاثاء',
    'الأربعاء',
    'الخميس',
    'الجمعة',
    'السبت'
  ];

  final List<String> _availableColors = [
    '#FF9800',
    '#2196F3',
    '#4CAF50',
    '#F44336',
    '#9C27B0',
    '#FF5722',
    '#795548',
    '#607D8B'
  ];

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.chef.name);
    _usernameController = TextEditingController(text: widget.chef.username);
    _specialtyController = TextEditingController(text: widget.chef.specialty);
    _descriptionController =
        TextEditingController(text: widget.chef.description ?? '');
    _startTimeController = TextEditingController(text: widget.chef.startTime);
    _endTimeController = TextEditingController(text: widget.chef.endTime);
    _selectedStatus = widget.chef.status;
    _selectedColor = widget.chef.color;
    _selectedDays = List.from(widget.chef.workingDays);
    _priority = widget.chef.priority;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _usernameController.dispose();
    _specialtyController.dispose();
    _descriptionController.dispose();
    _startTimeController.dispose();
    _endTimeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('تعديل الشيف ${widget.chef.name}'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'اسم الشيف',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال اسم الشيف';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _specialtyController,
                decoration: const InputDecoration(
                  labelText: 'التخصص',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال التخصص';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<ChefStatus>(
                value: _selectedStatus,
                decoration: const InputDecoration(
                  labelText: 'الحالة',
                  border: OutlineInputBorder(),
                ),
                items: ChefStatus.values.map((status) {
                  return DropdownMenuItem(
                    value: status,
                    child: Text(status.displayName),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedStatus = value!;
                  });
                },
              ),
              const SizedBox(height: 16),

              // اختيار اللون
              const Text('اللون المميز:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: _availableColors.map((color) {
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedColor = color;
                      });
                    },
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Color(int.parse(color.substring(1), radix: 16) +
                            0xFF000000),
                        shape: BoxShape.circle,
                        border: _selectedColor == color
                            ? Border.all(color: Colors.black, width: 3)
                            : null,
                      ),
                    ),
                  );
                }).toList(),
              ),

              const SizedBox(height: 16),

              // أيام العمل
              const Text('أيام العمل:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: _availableDays.map((day) {
                  return FilterChip(
                    label: Text(day),
                    selected: _selectedDays.contains(day),
                    onSelected: (selected) {
                      setState(() {
                        if (selected) {
                          _selectedDays.add(day);
                        } else {
                          _selectedDays.remove(day);
                        }
                      });
                    },
                  );
                }).toList(),
              ),

              const SizedBox(height: 16),

              // أوقات العمل
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _startTimeController,
                      decoration: const InputDecoration(
                        labelText: 'وقت البداية',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _endTimeController,
                      decoration: const InputDecoration(
                        labelText: 'وقت النهاية',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              final updatedChef = widget.chef.copyWith(
                name: _nameController.text.trim(),
                specialty: _specialtyController.text.trim(),
                description: _descriptionController.text.trim().isEmpty
                    ? null
                    : _descriptionController.text.trim(),
                color: _selectedColor,
                status: _selectedStatus,
                workingDays: _selectedDays,
                startTime: _startTimeController.text.trim(),
                endTime: _endTimeController.text.trim(),
              );

              widget.onChefUpdated(updatedChef);
              Navigator.pop(context);
            }
          },
          child: const Text('حفظ'),
        ),
      ],
    );
  }
}
