enum TableStatus { available, occupied, reserved, outOfService }

class RestaurantTable {
  final int? id;
  final int number;
  final int capacity;
  final TableStatus status;
  final String? location;
  final String? notes;
  final DateTime? reservationTime;
  final String? reservedBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  RestaurantTable({
    this.id,
    required this.number,
    required this.capacity,
    this.status = TableStatus.available,
    this.location,
    this.notes,
    this.reservationTime,
    this.reservedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  factory RestaurantTable.fromMap(Map<String, dynamic> map) {
    return RestaurantTable(
      id: map['id'],
      number: map['number'] ?? 0,
      capacity: map['capacity'] ?? 4,
      status: TableStatus.values[map['status'] ?? 0],
      location: map['location'],
      notes: map['notes'],
      reservationTime: map['reservation_time'] != null 
          ? DateTime.parse(map['reservation_time']) 
          : null,
      reservedBy: map['reserved_by'],
      createdAt: DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(map['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'number': number,
      'capacity': capacity,
      'status': status.index,
      'location': location,
      'notes': notes,
      'reservation_time': reservationTime?.toIso8601String(),
      'reserved_by': reservedBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  RestaurantTable copyWith({
    int? id,
    int? number,
    int? capacity,
    TableStatus? status,
    String? location,
    String? notes,
    DateTime? reservationTime,
    String? reservedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RestaurantTable(
      id: id ?? this.id,
      number: number ?? this.number,
      capacity: capacity ?? this.capacity,
      status: status ?? this.status,
      location: location ?? this.location,
      notes: notes ?? this.notes,
      reservationTime: reservationTime ?? this.reservationTime,
      reservedBy: reservedBy ?? this.reservedBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'RestaurantTable{id: $id, number: $number, capacity: $capacity, status: $status}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RestaurantTable && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

extension TableStatusExtension on TableStatus {
  String get displayName {
    switch (this) {
      case TableStatus.available:
        return 'متاحة';
      case TableStatus.occupied:
        return 'مشغولة';
      case TableStatus.reserved:
        return 'محجوزة';
      case TableStatus.outOfService:
        return 'خارج الخدمة';
    }
  }

  String get displayNameEn {
    switch (this) {
      case TableStatus.available:
        return 'Available';
      case TableStatus.occupied:
        return 'Occupied';
      case TableStatus.reserved:
        return 'Reserved';
      case TableStatus.outOfService:
        return 'Out of Service';
    }
  }
}
