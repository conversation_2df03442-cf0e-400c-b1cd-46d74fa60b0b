import 'package:sqflite/sqflite.dart';
import 'package:flutter/foundation.dart';
import '../models/menu_item.dart';
import 'database_service.dart';
import 'mock_data_service.dart';

class MenuService {
  static final MenuService _instance = MenuService._internal();
  factory MenuService() => _instance;
  MenuService._internal();

  final DatabaseService _databaseService = DatabaseService();

  // إضافة صنف جديد
  Future<int> addMenuItem(MenuItem item) async {
    final db = await _databaseService.database;
    final id = await db.insert('menu_items', item.toMap());
    return id;
  }

  // تحديث صنف موجود
  Future<int> updateMenuItem(MenuItem item) async {
    final db = await _databaseService.database;
    return await db.update(
      'menu_items',
      item.copyWith(updatedAt: DateTime.now()).toMap(),
      where: 'id = ?',
      whereArgs: [item.id],
    );
  }

  // حذف صنف
  Future<int> deleteMenuItem(String id) async {
    final db = await _databaseService.database;
    return await db.delete(
      'menu_items',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // الحصول على صنف بالمعرف
  Future<MenuItem?> getMenuItemById(String id) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'menu_items',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return MenuItem.fromMap(maps.first);
    }
    return null;
  }

  // الحصول على جميع الأصناف
  Future<List<MenuItem>> getAllMenuItems() async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'menu_items',
        orderBy: 'category, name',
      );

      return List.generate(maps.length, (i) {
        return MenuItem.fromMap(maps[i]);
      });
    } catch (e) {
      // استخدام البيانات التجريبية في حالة فشل قاعدة البيانات (مثل الويب)
      return MockDataService.mockMenuItems;
    }
  }

  // الحصول على الأصناف المتاحة فقط
  Future<List<MenuItem>> getAvailableMenuItems() async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'menu_items',
        where: 'is_available = ?',
        whereArgs: [1],
        orderBy: 'category, name',
      );

      return List.generate(maps.length, (i) {
        return MenuItem.fromMap(maps[i]);
      });
    } catch (e) {
      // استخدام البيانات التجريبية في حالة فشل قاعدة البيانات (مثل الويب)
      return MockDataService.mockMenuItems
          .where((item) => item.isAvailable)
          .toList();
    }
  }

  // الحصول على الأصناف حسب الفئة
  Future<List<MenuItem>> getMenuItemsByCategory(String category) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'menu_items',
      where: 'category = ? AND is_available = ?',
      whereArgs: [category, 1],
      orderBy: 'name',
    );

    return List.generate(maps.length, (i) {
      return MenuItem.fromMap(maps[i]);
    });
  }

  // البحث في الأصناف
  Future<List<MenuItem>> searchMenuItems(String query) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'menu_items',
      where:
          '(name LIKE ? OR name_en LIKE ? OR description LIKE ? OR description_en LIKE ?) AND is_available = ?',
      whereArgs: ['%$query%', '%$query%', '%$query%', '%$query%', 1],
      orderBy: 'category, name',
    );

    return List.generate(maps.length, (i) {
      return MenuItem.fromMap(maps[i]);
    });
  }

  // الحصول على الفئات المتاحة
  Future<List<String>> getAvailableCategories() async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.rawQuery(
        'SELECT DISTINCT category FROM menu_items WHERE is_available = 1 ORDER BY category',
      );

      return maps.map((map) => map['category'] as String).toList();
    } catch (e) {
      // استخدام البيانات التجريبية في حالة فشل قاعدة البيانات (مثل الويب)
      return MockDataService.mockCategories;
    }
  }

  // تغيير حالة توفر الصنف
  Future<int> toggleItemAvailability(String id) async {
    final db = await _databaseService.database;
    final item = await getMenuItemById(id);
    if (item != null) {
      return await updateMenuItem(
        item.copyWith(
          isAvailable: !item.isAvailable,
          updatedAt: DateTime.now(),
        ),
      );
    }
    return 0;
  }

  // الحصول على عدد الأصناف
  Future<int> getMenuItemsCount() async {
    final db = await _databaseService.database;
    final result =
        await db.rawQuery('SELECT COUNT(*) as count FROM menu_items');
    return Sqflite.firstIntValue(result) ?? 0;
  }

  // الحصول على عدد الأصناف المتاحة
  Future<int> getAvailableMenuItemsCount() async {
    final db = await _databaseService.database;
    final result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM menu_items WHERE is_available = 1');
    return Sqflite.firstIntValue(result) ?? 0;
  }

  // تحديث أسعار فئة معينة بنسبة مئوية
  Future<int> updateCategoryPrices(String category, double percentage) async {
    final db = await _databaseService.database;
    return await db.rawUpdate(
      'UPDATE menu_items SET price = price * ?, updated_at = ? WHERE category = ?',
      [1 + (percentage / 100), DateTime.now().toIso8601String(), category],
    );
  }

  // نسخ احتياطي للقائمة
  Future<List<Map<String, dynamic>>> exportMenuItems() async {
    final db = await _databaseService.database;
    return await db.query('menu_items', orderBy: 'category, name');
  }

  // استيراد قائمة من نسخة احتياطية
  Future<void> importMenuItems(List<Map<String, dynamic>> items) async {
    final db = await _databaseService.database;
    final batch = db.batch();

    for (final item in items) {
      // إزالة المعرف للسماح بإنشاء معرف جديد
      final itemData = Map<String, dynamic>.from(item);
      itemData.remove('id');
      itemData['created_at'] = DateTime.now().toIso8601String();
      itemData['updated_at'] = DateTime.now().toIso8601String();

      batch.insert('menu_items', itemData);
    }

    await batch.commit();
  }

  // حذف جميع الأصناف (للاختبار)
  Future<int> deleteAllMenuItems() async {
    final db = await _databaseService.database;
    return await db.delete('menu_items');
  }
}
