import 'package:flutter/material.dart';
import '../../models/kitchen_order.dart';
import '../../models/order.dart';

class KitchenOrderCard extends StatelessWidget {
  final KitchenOrderItem order;
  final Function(KitchenOrderItem, KitchenOrderStatus) onStatusUpdate;

  const KitchenOrderCard({
    super.key,
    required this.order,
    required this.onStatusUpdate,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: _getStatusColor(),
            width: 2,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              _buildHeader(),
              const SizedBox(height: 12),
              
              // تفاصيل الصنف
              _buildItemDetails(),
              const SizedBox(height: 12),
              
              // الملاحظات
              if (order.notes.isNotEmpty) ...[
                _buildNotes(),
                const SizedBox(height: 12),
              ],
              
              // معلومات الوقت
              _buildTimeInfo(),
              const SizedBox(height: 16),
              
              // أزرار الحالة
              _buildStatusButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        // رقم الطلب
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.blue,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            'طلب #${order.orderId.substring(0, 8)}',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
        const SizedBox(width: 8),
        
        // رقم الطاولة أو نوع الطلب
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            'طاولة ${order.orderId}', // سيتم تحديثه لاحقاً
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        
        const Spacer(),
        
        // حالة الطلب
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getStatusColor(),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            order.status.displayName,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildItemDetails() {
    return Row(
      children: [
        // صورة الصنف
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            image: order.menuItem.imagePath != null
                ? DecorationImage(
                    image: NetworkImage(order.menuItem.imagePath!),
                    fit: BoxFit.cover,
                  )
                : null,
            color: order.menuItem.imagePath == null ? Colors.grey[300] : null,
          ),
          child: order.menuItem.imagePath == null
              ? const Icon(Icons.fastfood, color: Colors.grey)
              : null,
        ),
        const SizedBox(width: 12),
        
        // تفاصيل الصنف
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                order.menuItem.name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                order.menuItem.description,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        
        // الكمية
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.orange,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Center(
            child: Text(
              'x${order.quantity}',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNotes() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.yellow[50],
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.yellow[300]!),
      ),
      child: Row(
        children: [
          Icon(Icons.note, color: Colors.yellow[700], size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              order.notes,
              style: TextStyle(
                fontSize: 12,
                color: Colors.yellow[800],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeInfo() {
    final elapsed = order.elapsedTime;
    final estimated = order.estimatedTime;
    final isDelayed = order.isDelayed;

    return Row(
      children: [
        Icon(
          Icons.access_time,
          size: 16,
          color: isDelayed ? Colors.red : Colors.grey[600],
        ),
        const SizedBox(width: 4),
        Text(
          'الوقت المنقضي: ${_formatDuration(elapsed)}',
          style: TextStyle(
            fontSize: 12,
            color: isDelayed ? Colors.red : Colors.grey[600],
            fontWeight: isDelayed ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        const SizedBox(width: 16),
        Icon(
          Icons.timer,
          size: 16,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 4),
        Text(
          'المتوقع: ${_formatDuration(estimated)}',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        if (isDelayed) ...[
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.red,
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Text(
              'متأخر',
              style: TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildStatusButtons() {
    return Row(
      children: [
        if (order.status == KitchenOrderStatus.pending) ...[
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => onStatusUpdate(order, KitchenOrderStatus.inProgress),
              icon: const Icon(Icons.play_arrow),
              label: const Text('بدء التحضير'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
          ),
          const SizedBox(width: 8),
          ElevatedButton.icon(
            onPressed: () => onStatusUpdate(order, KitchenOrderStatus.cancelled),
            icon: const Icon(Icons.cancel),
            label: const Text('إلغاء'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
          ),
        ] else if (order.status == KitchenOrderStatus.inProgress) ...[
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => onStatusUpdate(order, KitchenOrderStatus.ready),
              icon: const Icon(Icons.check_circle),
              label: const Text('جاهز'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
              ),
            ),
          ),
          const SizedBox(width: 8),
          ElevatedButton.icon(
            onPressed: () => onStatusUpdate(order, KitchenOrderStatus.cancelled),
            icon: const Icon(Icons.cancel),
            label: const Text('إلغاء'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
          ),
        ] else if (order.status == KitchenOrderStatus.ready) ...[
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => onStatusUpdate(order, KitchenOrderStatus.completed),
              icon: const Icon(Icons.done_all),
              label: const Text('تم التسليم'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Color _getStatusColor() {
    switch (order.status) {
      case KitchenOrderStatus.pending:
        return order.isDelayed ? Colors.red : Colors.blue;
      case KitchenOrderStatus.inProgress:
        return order.isDelayed ? Colors.orange : Colors.green;
      case KitchenOrderStatus.ready:
        return Colors.purple;
      case KitchenOrderStatus.completed:
        return Colors.grey;
      case KitchenOrderStatus.cancelled:
        return Colors.red;
    }
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes}:${seconds.toString().padLeft(2, '0')}';
  }
}
