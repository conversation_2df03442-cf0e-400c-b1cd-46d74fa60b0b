import '../models/user_role.dart';
import '../models/chef.dart';
import 'restaurant_config_service.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final RestaurantConfigService _configService = RestaurantConfigService();

  // المستخدمون الافتراضيون
  final Map<String, Map<String, String>> _defaultUsers = {
    'admin': {'password': 'admin', 'name': 'مدير النظام', 'role': 'manager'},
    'cashier': {'password': '123456', 'name': 'الكاشير', 'role': 'cashier'},
    'waiter': {'password': '123456', 'name': 'النادل', 'role': 'waiter'},
  };

  // تسجيل الدخول
  Future<User?> login(String username, String password) async {
    try {
      // التحقق من المستخدمين الافتراضيين أولاً
      if (_defaultUsers.containsKey(username)) {
        final userData = _defaultUsers[username]!;
        if (userData['password'] == password) {
          return User(
            id: username,
            username: username,
            name: userData['name']!,
            role: _getUserRole(userData['role']!),
            createdAt: DateTime.now(),
            lastLogin: DateTime.now(),
          );
        }
      }

      // التحقق من الشيفات في قاعدة البيانات
      final chefs = await _configService.getAllChefs();
      for (final chef in chefs) {
        if (chef.username == username && chef.password == password) {
          if (chef.status == ChefStatus.active) {
            return User(
              id: chef.id,
              username: chef.username,
              name: chef.name,
              role: _getChefRole(chef),
              createdAt: chef.createdAt,
              lastLogin: DateTime.now(),
            );
          }
        }
      }

      return null; // فشل تسجيل الدخول
    } catch (e) {
      print('خطأ في تسجيل الدخول: $e');
      return null;
    }
  }

  // تحويل نص الدور إلى UserRole
  UserRole _getUserRole(String roleString) {
    switch (roleString) {
      case 'manager':
        return UserRole.manager;
      case 'cashier':
        return UserRole.cashier;
      case 'waiter':
        return UserRole.waiter;
      default:
        return UserRole.cashier;
    }
  }

  // تحديد دور الشيف بناءً على تخصصه
  UserRole _getChefRole(Chef chef) {
    // تحديد الدور بناءً على التخصص
    final specialty = chef.specialty.toLowerCase();

    if (specialty.contains('بيتزا') || specialty.contains('pizza')) {
      return UserRole.pizzaChef;
    } else if (specialty.contains('برجر') || specialty.contains('burger')) {
      return UserRole.burgerChef;
    } else {
      return UserRole.generalChef;
    }
  }

  // الحصول على مسار الشاشة الرئيسية للشيف
  String getChefHomeRoute(Chef chef) {
    // جميع الشيفات يذهبون لنفس الشاشة المرنة
    return '/kitchen/chef';
  }

  // التحقق من صحة بيانات تسجيل الدخول
  bool validateCredentials(String username, String password) {
    return username.isNotEmpty && password.isNotEmpty;
  }

  // تسجيل الخروج
  Future<void> logout() async {
    // يمكن إضافة منطق تسجيل الخروج هنا
    // مثل تحديث وقت آخر تسجيل خروج
  }

  // التحقق من وجود المستخدم
  Future<bool> userExists(String username) async {
    // التحقق من المستخدمين الافتراضيين
    if (_defaultUsers.containsKey(username)) {
      return true;
    }

    // التحقق من الشيفات
    try {
      final chefs = await _configService.getAllChefs();
      return chefs.any((chef) => chef.username == username);
    } catch (e) {
      return false;
    }
  }

  // الحصول على جميع المستخدمين (للإدارة)
  Future<List<User>> getAllUsers() async {
    final List<User> users = [];

    // إضافة المستخدمين الافتراضيين
    _defaultUsers.forEach((username, userData) {
      users.add(User(
        id: username,
        username: username,
        name: userData['name']!,
        role: _getUserRole(userData['role']!),
        createdAt: DateTime.now(),
      ));
    });

    // إضافة الشيفات
    try {
      final chefs = await _configService.getAllChefs();
      for (final chef in chefs) {
        users.add(User(
          id: chef.id,
          username: chef.username,
          name: chef.name,
          role: _getChefRole(chef),
          isActive: chef.status == ChefStatus.active,
          createdAt: chef.createdAt,
          lastLogin: chef.lastLogin,
        ));
      }
    } catch (e) {
      print('خطأ في تحميل الشيفات: $e');
    }

    return users;
  }

  // تحديث كلمة مرور الشيف
  Future<bool> updateChefPassword(String chefId, String newPassword) async {
    try {
      final chefs = await _configService.getAllChefs();
      final chef = chefs.firstWhere((c) => c.id == chefId);

      final updatedChef = chef.copyWith(password: newPassword);
      await _configService.updateChef(updatedChef);

      return true;
    } catch (e) {
      print('خطأ في تحديث كلمة المرور: $e');
      return false;
    }
  }

  // تحديث حالة الشيف
  Future<bool> updateChefStatus(String chefId, ChefStatus status) async {
    try {
      final chefs = await _configService.getAllChefs();
      final chef = chefs.firstWhere((c) => c.id == chefId);

      final updatedChef = chef.copyWith(status: status);
      await _configService.updateChef(updatedChef);

      return true;
    } catch (e) {
      print('خطأ في تحديث حالة الشيف: $e');
      return false;
    }
  }
}
