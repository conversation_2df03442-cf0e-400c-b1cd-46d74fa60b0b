import 'package:sqflite/sqflite.dart';
import '../models/bill.dart';
import '../models/order.dart';
import 'database_service.dart';

class BillService {
  static final BillService _instance = BillService._internal();
  factory BillService() => _instance;
  BillService._internal();

  final DatabaseService _databaseService = DatabaseService();

  // إنشاء فاتورة جديدة
  Future<int> createBill(Bill bill) async {
    final db = await _databaseService.database;
    final id = await db.insert('bills', bill.toMap());
    return id;
  }

  // تحديث فاتورة موجودة
  Future<int> updateBill(Bill bill) async {
    final db = await _databaseService.database;
    return await db.update(
      'bills',
      bill.copyWith(updatedAt: DateTime.now()).toMap(),
      where: 'id = ?',
      whereArgs: [bill.id],
    );
  }

  // حذف فاتورة
  Future<int> deleteBill(int id) async {
    final db = await _databaseService.database;
    return await db.delete(
      'bills',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // الحصول على فاتورة بالمعرف
  Future<Bill?> getBillById(int id) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'bills',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Bill.fromMap(maps.first);
    }
    return null;
  }

  // الحصول على فاتورة برقمها
  Future<Bill?> getBillByNumber(String billNumber) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'bills',
      where: 'bill_number = ?',
      whereArgs: [billNumber],
    );

    if (maps.isNotEmpty) {
      return Bill.fromMap(maps.first);
    }
    return null;
  }

  // الحصول على جميع الفواتير
  Future<List<Bill>> getAllBills() async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'bills',
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Bill.fromMap(maps[i]);
    });
  }

  // الحصول على فواتير يوم معين
  Future<List<Bill>> getBillsByDate(DateTime date) async {
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'bills',
      where: 'created_at >= ? AND created_at < ?',
      whereArgs: [startOfDay.toIso8601String(), endOfDay.toIso8601String()],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Bill.fromMap(maps[i]);
    });
  }

  // الحصول على فواتير فترة معينة
  Future<List<Bill>> getBillsByDateRange(DateTime startDate, DateTime endDate) async {
    final start = DateTime(startDate.year, startDate.month, startDate.day);
    final end = DateTime(endDate.year, endDate.month, endDate.day).add(const Duration(days: 1));

    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'bills',
      where: 'created_at >= ? AND created_at < ?',
      whereArgs: [start.toIso8601String(), end.toIso8601String()],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Bill.fromMap(maps[i]);
    });
  }

  // الحصول على فواتير حسب طريقة الدفع
  Future<List<Bill>> getBillsByPaymentMethod(PaymentMethod paymentMethod) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'bills',
      where: 'payment_method = ?',
      whereArgs: [paymentMethod.index],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Bill.fromMap(maps[i]);
    });
  }

  // إنشاء رقم فاتورة جديد
  Future<String> generateBillNumber() async {
    final now = DateTime.now();
    final datePrefix = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}';
    
    final db = await _databaseService.database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM bills WHERE bill_number LIKE ?',
      ['$datePrefix%'],
    );
    
    final count = Sqflite.firstIntValue(result) ?? 0;
    return '$datePrefix${(count + 1).toString().padLeft(4, '0')}';
  }

  // البحث في الفواتير
  Future<List<Bill>> searchBills(String query) async {
    final db = await _databaseService.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'bills',
      where: 'bill_number LIKE ? OR payment_reference LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return Bill.fromMap(maps[i]);
    });
  }

  // الحصول على مبيعات يوم معين
  Future<DailySales> getDailySales(DateTime date) async {
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    final db = await _databaseService.database;
    
    // إجمالي الطلبات والمبيعات
    final totalResult = await db.rawQuery('''
      SELECT 
        COUNT(*) as total_orders,
        COALESCE(SUM(total_amount), 0) as total_sales,
        COALESCE(SUM(tax_amount), 0) as total_tax,
        COALESCE(SUM(discount_amount), 0) as total_discount
      FROM bills 
      WHERE created_at >= ? AND created_at < ?
    ''', [startOfDay.toIso8601String(), endOfDay.toIso8601String()]);

    // المبيعات حسب طريقة الدفع
    final cashResult = await db.rawQuery('''
      SELECT COALESCE(SUM(total_amount), 0) as cash_sales
      FROM bills 
      WHERE created_at >= ? AND created_at < ? AND payment_method = ?
    ''', [startOfDay.toIso8601String(), endOfDay.toIso8601String(), PaymentMethod.cash.index]);

    final cardResult = await db.rawQuery('''
      SELECT COALESCE(SUM(total_amount), 0) as card_sales
      FROM bills 
      WHERE created_at >= ? AND created_at < ? AND payment_method = ?
    ''', [startOfDay.toIso8601String(), endOfDay.toIso8601String(), PaymentMethod.card.index]);

    final deferredResult = await db.rawQuery('''
      SELECT COALESCE(SUM(total_amount), 0) as deferred_sales
      FROM bills 
      WHERE created_at >= ? AND created_at < ? AND payment_method = ?
    ''', [startOfDay.toIso8601String(), endOfDay.toIso8601String(), PaymentMethod.deferred.index]);

    final total = totalResult.first;
    final cash = cashResult.first;
    final card = cardResult.first;
    final deferred = deferredResult.first;

    return DailySales(
      date: date,
      totalOrders: total['total_orders'] as int,
      totalSales: (total['total_sales'] as num).toDouble(),
      cashSales: (cash['cash_sales'] as num).toDouble(),
      cardSales: (card['card_sales'] as num).toDouble(),
      deferredSales: (deferred['deferred_sales'] as num).toDouble(),
      totalTax: (total['total_tax'] as num).toDouble(),
      totalDiscount: (total['total_discount'] as num).toDouble(),
    );
  }

  // الحصول على مبيعات فترة معينة
  Future<List<DailySales>> getSalesReport(DateTime startDate, DateTime endDate) async {
    final List<DailySales> salesList = [];
    DateTime currentDate = DateTime(startDate.year, startDate.month, startDate.day);
    final end = DateTime(endDate.year, endDate.month, endDate.day);

    while (currentDate.isBefore(end) || currentDate.isAtSameMomentAs(end)) {
      final dailySales = await getDailySales(currentDate);
      salesList.add(dailySales);
      currentDate = currentDate.add(const Duration(days: 1));
    }

    return salesList;
  }

  // إحصائيات الفواتير
  Future<Map<String, dynamic>> getBillsStatistics() async {
    final db = await _databaseService.database;
    
    final totalResult = await db.rawQuery('SELECT COUNT(*) as count FROM bills');
    final todayResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM bills WHERE DATE(created_at) = DATE(?)',
      [DateTime.now().toIso8601String()],
    );
    final totalSalesResult = await db.rawQuery('SELECT COALESCE(SUM(total_amount), 0) as total FROM bills');
    final todaySalesResult = await db.rawQuery(
      'SELECT COALESCE(SUM(total_amount), 0) as total FROM bills WHERE DATE(created_at) = DATE(?)',
      [DateTime.now().toIso8601String()],
    );

    return {
      'totalBills': Sqflite.firstIntValue(totalResult) ?? 0,
      'todayBills': Sqflite.firstIntValue(todayResult) ?? 0,
      'totalSales': (totalSalesResult.first['total'] as num).toDouble(),
      'todaySales': (todaySalesResult.first['total'] as num).toDouble(),
    };
  }

  // الحصول على أفضل الأصناف مبيعاً
  Future<List<Map<String, dynamic>>> getTopSellingItems({int limit = 10}) async {
    final db = await _databaseService.database;
    
    final result = await db.rawQuery('''
      SELECT 
        mi.name,
        mi.name_en,
        SUM(oi.quantity) as total_quantity,
        SUM(oi.quantity * oi.unit_price) as total_revenue
      FROM order_items oi
      JOIN menu_items mi ON oi.menu_item_id = mi.id
      JOIN orders o ON oi.order_id = o.id
      JOIN bills b ON o.id = b.order_id
      GROUP BY mi.id, mi.name, mi.name_en
      ORDER BY total_quantity DESC
      LIMIT ?
    ''', [limit]);

    return result;
  }

  // حذف جميع الفواتير (للاختبار)
  Future<int> deleteAllBills() async {
    final db = await _databaseService.database;
    return await db.delete('bills');
  }
}
