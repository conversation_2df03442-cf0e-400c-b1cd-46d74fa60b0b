com.example.restaurant_management_system.app-jetified-savedstate-1.2.1-0 C:\Users\<USER>\.gradle\caches\transforms-3\010887bb3359843376c02ecf4bf2ef43\transformed\jetified-savedstate-1.2.1\res
com.example.restaurant_management_system.app-jetified-activity-1.9.3-1 C:\Users\<USER>\.gradle\caches\transforms-3\01880ab3b0e2d1af0a34ca552f67d88d\transformed\jetified-activity-1.9.3\res
com.example.restaurant_management_system.app-core-runtime-2.2.0-2 C:\Users\<USER>\.gradle\caches\transforms-3\08235bf0a8760a790ea7b8b4eab82236\transformed\core-runtime-2.2.0\res
com.example.restaurant_management_system.app-jetified-profileinstaller-1.3.1-3 C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\res
com.example.restaurant_management_system.app-jetified-window-1.2.0-4 C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\res
com.example.restaurant_management_system.app-jetified-lifecycle-livedata-core-ktx-2.7.0-5 C:\Users\<USER>\.gradle\caches\transforms-3\4befdc7f9fed1e5c6647eae72672d219\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.restaurant_management_system.app-lifecycle-viewmodel-2.7.0-6 C:\Users\<USER>\.gradle\caches\transforms-3\52dfd6d7c21c1e36dbdf0d304093d642\transformed\lifecycle-viewmodel-2.7.0\res
com.example.restaurant_management_system.app-jetified-startup-runtime-1.1.1-7 C:\Users\<USER>\.gradle\caches\transforms-3\53f114d110e356c36b5f50ab357f361f\transformed\jetified-startup-runtime-1.1.1\res
com.example.restaurant_management_system.app-jetified-core-ktx-1.13.1-8 C:\Users\<USER>\.gradle\caches\transforms-3\58accd456bc756075136cc5a7dae18d3\transformed\jetified-core-ktx-1.13.1\res
com.example.restaurant_management_system.app-jetified-lifecycle-process-2.7.0-9 C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\res
com.example.restaurant_management_system.app-jetified-annotation-experimental-1.4.0-10 C:\Users\<USER>\.gradle\caches\transforms-3\6707c561045b071954f9f1d8398a2530\transformed\jetified-annotation-experimental-1.4.0\res
com.example.restaurant_management_system.app-jetified-core-1.0.0-11 C:\Users\<USER>\.gradle\caches\transforms-3\6dde453803d512b3f23cc981e264e9e0\transformed\jetified-core-1.0.0\res
com.example.restaurant_management_system.app-lifecycle-livedata-2.7.0-12 C:\Users\<USER>\.gradle\caches\transforms-3\9c28b5c7a2d23237247570dbf8382af1\transformed\lifecycle-livedata-2.7.0\res
com.example.restaurant_management_system.app-lifecycle-livedata-core-2.7.0-13 C:\Users\<USER>\.gradle\caches\transforms-3\9ec76326f098fb11057ae8a1fda52a48\transformed\lifecycle-livedata-core-2.7.0\res
com.example.restaurant_management_system.app-fragment-1.7.1-14 C:\Users\<USER>\.gradle\caches\transforms-3\a49cd171355a6a39687e69f028c8bcbc\transformed\fragment-1.7.1\res
com.example.restaurant_management_system.app-jetified-window-java-1.2.0-15 C:\Users\<USER>\.gradle\caches\transforms-3\bf78d940cc78ca45c77421e3af64376e\transformed\jetified-window-java-1.2.0\res
com.example.restaurant_management_system.app-core-1.13.1-16 C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\res
com.example.restaurant_management_system.app-lifecycle-runtime-2.7.0-17 C:\Users\<USER>\.gradle\caches\transforms-3\c8996eb6ce8efabee1883fe1486006c9\transformed\lifecycle-runtime-2.7.0\res
com.example.restaurant_management_system.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-18 C:\Users\<USER>\.gradle\caches\transforms-3\ca2eed4266fd24517e583e6618aa3924\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.restaurant_management_system.app-jetified-tracing-1.2.0-19 C:\Users\<USER>\.gradle\caches\transforms-3\fee79d0d8ef3af5dbc14ef6d22d028cf\transformed\jetified-tracing-1.2.0\res
com.example.restaurant_management_system.app-debug-20 D:\PROJECTS\restaurant_management_system\restaurant_management_system\android\app\src\debug\res
com.example.restaurant_management_system.app-main-21 D:\PROJECTS\restaurant_management_system\restaurant_management_system\android\app\src\main\res
com.example.restaurant_management_system.app-pngs-22 D:\PROJECTS\restaurant_management_system\restaurant_management_system\build\app\generated\res\pngs\debug
com.example.restaurant_management_system.app-resValues-23 D:\PROJECTS\restaurant_management_system\restaurant_management_system\build\app\generated\res\resValues\debug
com.example.restaurant_management_system.app-packageDebugResources-24 D:\PROJECTS\restaurant_management_system\restaurant_management_system\build\app\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.restaurant_management_system.app-packageDebugResources-25 D:\PROJECTS\restaurant_management_system\restaurant_management_system\build\app\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.restaurant_management_system.app-merged_res-26 D:\PROJECTS\restaurant_management_system\restaurant_management_system\build\app\intermediates\merged_res\debug
com.example.restaurant_management_system.app-packaged_res-27 D:\PROJECTS\restaurant_management_system\restaurant_management_system\build\flutter_plugin_android_lifecycle\intermediates\packaged_res\debug
com.example.restaurant_management_system.app-packaged_res-28 D:\PROJECTS\restaurant_management_system\restaurant_management_system\build\image_picker_android\intermediates\packaged_res\debug
com.example.restaurant_management_system.app-packaged_res-29 D:\PROJECTS\restaurant_management_system\restaurant_management_system\build\path_provider_android\intermediates\packaged_res\debug
com.example.restaurant_management_system.app-packaged_res-30 D:\PROJECTS\restaurant_management_system\restaurant_management_system\build\sqflite_android\intermediates\packaged_res\debug
