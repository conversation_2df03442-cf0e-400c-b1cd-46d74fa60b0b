import 'package:flutter/material.dart';
import '../../models/restaurant_table.dart';
import '../../services/restaurant_config_service.dart';

class TablesManagementScreen extends StatefulWidget {
  const TablesManagementScreen({super.key});

  @override
  State<TablesManagementScreen> createState() => _TablesManagementScreenState();
}

class _TablesManagementScreenState extends State<TablesManagementScreen> {
  final RestaurantConfigService _configService = RestaurantConfigService();
  List<RestaurantTable> _tables = [];
  bool _isLoading = true;
  String _selectedSection = 'الكل';
  List<String> _sections = ['الكل'];

  @override
  void initState() {
    super.initState();
    _loadTables();
  }

  Future<void> _loadTables() async {
    setState(() => _isLoading = true);
    try {
      final tables = await _configService.getAllTables();
      final sections = tables.map((t) => t.section).toSet().toList();
      sections.sort();
      
      setState(() {
        _tables = tables;
        _sections = ['الكل', ...sections];
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showError('خطأ في تحميل الطاولات: $e');
    }
  }

  List<RestaurantTable> get _filteredTables {
    if (_selectedSection == 'الكل') {
      return _tables;
    }
    return _tables.where((table) => table.section == _selectedSection).toList();
  }

  void _showAddTableDialog() {
    showDialog(
      context: context,
      builder: (context) => AddTableDialog(
        onTableAdded: (table) async {
          try {
            await _configService.addTable(table);
            _loadTables();
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إضافة الطاولة بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          } catch (e) {
            _showError('خطأ في إضافة الطاولة: $e');
          }
        },
      ),
    );
  }

  void _showEditTableDialog(RestaurantTable table) {
    showDialog(
      context: context,
      builder: (context) => EditTableDialog(
        table: table,
        onTableUpdated: (updatedTable) async {
          try {
            await _configService.updateTable(updatedTable);
            _loadTables();
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم تحديث الطاولة بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          } catch (e) {
            _showError('خطأ في تحديث الطاولة: $e');
          }
        },
      ),
    );
  }

  void _deleteTable(RestaurantTable table) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الطاولة'),
        content: Text('هل أنت متأكد من حذف الطاولة ${table.fullName}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await _configService.deleteTable(table.id);
                _loadTables();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف الطاولة بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                _showError('خطأ في حذف الطاولة: $e');
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'إدارة الطاولات',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showAddTableDialog,
            icon: const Icon(Icons.add),
          ),
          IconButton(
            onPressed: _loadTables,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // فلتر الأقسام
                Container(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      const Text(
                        'القسم: ',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Expanded(
                        child: DropdownButton<String>(
                          value: _selectedSection,
                          isExpanded: true,
                          items: _sections.map((section) {
                            return DropdownMenuItem(
                              value: section,
                              child: Text(section),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedSection = value!;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                
                // إحصائيات سريعة
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    children: [
                      _buildStatCard(
                        'إجمالي الطاولات',
                        _filteredTables.length.toString(),
                        Colors.blue,
                      ),
                      const SizedBox(width: 8),
                      _buildStatCard(
                        'متاحة',
                        _filteredTables.where((t) => t.status == TableStatus.available).length.toString(),
                        Colors.green,
                      ),
                      const SizedBox(width: 8),
                      _buildStatCard(
                        'مشغولة',
                        _filteredTables.where((t) => t.status == TableStatus.occupied).length.toString(),
                        Colors.red,
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // قائمة الطاولات
                Expanded(
                  child: _filteredTables.isEmpty
                      ? const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.table_restaurant, size: 64, color: Colors.grey),
                              SizedBox(height: 16),
                              Text(
                                'لا توجد طاولات',
                                style: TextStyle(fontSize: 18, color: Colors.grey),
                              ),
                            ],
                          ),
                        )
                      : GridView.builder(
                          padding: const EdgeInsets.all(16),
                          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            childAspectRatio: 1.2,
                            crossAxisSpacing: 16,
                            mainAxisSpacing: 16,
                          ),
                          itemCount: _filteredTables.length,
                          itemBuilder: (context, index) {
                            final table = _filteredTables[index];
                            return _buildTableCard(table);
                          },
                        ),
                ),
              ],
            ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                title,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTableCard(RestaurantTable table) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: () => _showEditTableDialog(table),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Color(int.parse(table.statusColor.substring(1), radix: 16) + 0xFF000000),
              width: 2,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      table.number,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    PopupMenuButton(
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit),
                              SizedBox(width: 8),
                              Text('تعديل'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, color: Colors.red),
                              SizedBox(width: 8),
                              Text('حذف', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                      ],
                      onSelected: (value) {
                        if (value == 'edit') {
                          _showEditTableDialog(table);
                        } else if (value == 'delete') {
                          _deleteTable(table);
                        }
                      },
                    ),
                  ],
                ),
                Text(
                  table.section,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.people, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text('${table.capacity} أشخاص'),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(Icons.category, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(table.type.displayName),
                  ],
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Color(int.parse(table.statusColor.substring(1), radix: 16) + 0xFF000000),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    table.status.displayName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Dialog لإضافة طاولة جديدة
class AddTableDialog extends StatefulWidget {
  final Function(RestaurantTable) onTableAdded;

  const AddTableDialog({super.key, required this.onTableAdded});

  @override
  State<AddTableDialog> createState() => _AddTableDialogState();
}

class _AddTableDialogState extends State<AddTableDialog> {
  final _formKey = GlobalKey<FormState>();
  final _numberController = TextEditingController();
  final _sectionController = TextEditingController();
  final _capacityController = TextEditingController();
  final _notesController = TextEditingController();
  
  TableType _selectedType = TableType.regular;
  TableStatus _selectedStatus = TableStatus.available;

  @override
  void dispose() {
    _numberController.dispose();
    _sectionController.dispose();
    _capacityController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إضافة طاولة جديدة'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _numberController,
                decoration: const InputDecoration(
                  labelText: 'رقم الطاولة',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال رقم الطاولة';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _sectionController,
                decoration: const InputDecoration(
                  labelText: 'القسم',
                  border: OutlineInputBorder(),
                  hintText: 'القاعة الرئيسية',
                ),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _capacityController,
                decoration: const InputDecoration(
                  labelText: 'عدد الأشخاص',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال عدد الأشخاص';
                  }
                  final capacity = int.tryParse(value);
                  if (capacity == null || capacity <= 0) {
                    return 'عدد غير صحيح';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<TableType>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'نوع الطاولة',
                  border: OutlineInputBorder(),
                ),
                items: TableType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(type.displayName),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedType = value!;
                  });
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات (اختياري)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              final table = RestaurantTable(
                id: 'table_${DateTime.now().millisecondsSinceEpoch}',
                number: _numberController.text.trim(),
                section: _sectionController.text.trim().isEmpty 
                    ? 'القاعة الرئيسية' 
                    : _sectionController.text.trim(),
                capacity: int.parse(_capacityController.text),
                type: _selectedType,
                status: _selectedStatus,
                notes: _notesController.text.trim().isEmpty 
                    ? null 
                    : _notesController.text.trim(),
                createdAt: DateTime.now(),
              );
              
              widget.onTableAdded(table);
              Navigator.pop(context);
            }
          },
          child: const Text('إضافة'),
        ),
      ],
    );
  }
}

// Dialog لتعديل طاولة
class EditTableDialog extends StatefulWidget {
  final RestaurantTable table;
  final Function(RestaurantTable) onTableUpdated;

  const EditTableDialog({
    super.key, 
    required this.table, 
    required this.onTableUpdated,
  });

  @override
  State<EditTableDialog> createState() => _EditTableDialogState();
}

class _EditTableDialogState extends State<EditTableDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _numberController;
  late final TextEditingController _sectionController;
  late final TextEditingController _capacityController;
  late final TextEditingController _notesController;
  
  late TableType _selectedType;
  late TableStatus _selectedStatus;

  @override
  void initState() {
    super.initState();
    _numberController = TextEditingController(text: widget.table.number);
    _sectionController = TextEditingController(text: widget.table.section);
    _capacityController = TextEditingController(text: widget.table.capacity.toString());
    _notesController = TextEditingController(text: widget.table.notes ?? '');
    _selectedType = widget.table.type;
    _selectedStatus = widget.table.status;
  }

  @override
  void dispose() {
    _numberController.dispose();
    _sectionController.dispose();
    _capacityController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('تعديل الطاولة ${widget.table.number}'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _numberController,
                decoration: const InputDecoration(
                  labelText: 'رقم الطاولة',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال رقم الطاولة';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _sectionController,
                decoration: const InputDecoration(
                  labelText: 'القسم',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _capacityController,
                decoration: const InputDecoration(
                  labelText: 'عدد الأشخاص',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال عدد الأشخاص';
                  }
                  final capacity = int.tryParse(value);
                  if (capacity == null || capacity <= 0) {
                    return 'عدد غير صحيح';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<TableType>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'نوع الطاولة',
                  border: OutlineInputBorder(),
                ),
                items: TableType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(type.displayName),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedType = value!;
                  });
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<TableStatus>(
                value: _selectedStatus,
                decoration: const InputDecoration(
                  labelText: 'حالة الطاولة',
                  border: OutlineInputBorder(),
                ),
                items: TableStatus.values.map((status) {
                  return DropdownMenuItem(
                    value: status,
                    child: Text(status.displayName),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedStatus = value!;
                  });
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              final updatedTable = widget.table.copyWith(
                number: _numberController.text.trim(),
                section: _sectionController.text.trim(),
                capacity: int.parse(_capacityController.text),
                type: _selectedType,
                status: _selectedStatus,
                notes: _notesController.text.trim().isEmpty 
                    ? null 
                    : _notesController.text.trim(),
                lastUpdated: DateTime.now(),
              );
              
              widget.onTableUpdated(updatedTable);
              Navigator.pop(context);
            }
          },
          child: const Text('حفظ'),
        ),
      ],
    );
  }
}
